package com.jinghang.cash.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 项目信息VO
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:57
 */
@Data
public class ProjectInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目唯一编码
     */
    @NotBlank(message = "项目编码不能为空")
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 资产方编码 (关联资产方表)
     */
    private String flowChannel;

    /**
     * 融担方编码 (关联融担方表)
     */
    private String guaranteeCode;

    /**
     * 资金方编码 (关联资金方表)
     */
    private String capitalChannel;

    /**
     * 项目类型编码 (关联项目类型表)
     */
    private String projectTypeCode;

    /**
     * 项目状态 (ENABLE/DISABLE)
     */
    private String enabled;

    /**
     * 项目开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    /**
     * 项目结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    /**
     * 项目要素信息
     */
    private ProjectElementsDto elements;

    /**
     * 项目要素扩展信息
     */
    private ProjectElementsExtDto elementsExt;


    /**
     * 判断项目是否启用
     */
    public boolean isEnabled() {
        return "ENABLE".equals(enabled);
    }

    /**
     * 判断项目是否在有效期内
     */
    public boolean isInValidPeriod() {
        LocalDate now = LocalDate.now();

        // 检查开始日期
        if (startDate != null && now.isBefore(startDate)) {
            return false;
        }

        // 检查结束日期
        if (endDate != null && now.isAfter(endDate)) {
            return false;
        }

        return true;
    }

    /**
     * 判断项目是否有效（启用且在有效期内）
     */
    public boolean isValid() {
        return isEnabled() && isInValidPeriod();
    }
}
