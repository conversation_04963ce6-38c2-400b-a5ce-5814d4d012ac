package com.jinghang.cash.api;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 项目信息API接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 13:37
 */
public interface ProjectInfoApiService {

    /**
     * 根据项目编码查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息
     */
    @GetMapping("/projectInfo/{projectCode}")
    ProjectInfoDto queryProjectInfo(@PathVariable("projectCode") String projectCode);
}
