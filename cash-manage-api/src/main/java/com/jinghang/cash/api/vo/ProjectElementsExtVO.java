package com.jinghang.cash.api.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 项目要素扩展实体
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:56
 */
@TableName(value = "project_elements_ext")
@Data
public class ProjectElementsExtVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 上级要素ID
     */
    private String parentId;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    /**
     * 年利率基数(天) (如 360或365)
     */
    private String interestDaysBasis;

    /**
     * 是否支持线下跨日还款
     */
    private String allowCrossDayRepay;

    /**
     * 风控模型渠道
     */
    private String riskModelChannel;

    /**
     * 放款支付渠道
     */
    private String loanPaymentChannel;

    /**
     * 扣款绑卡渠道
     */
    private String deductionBindCardChannel;

    /**
     * 扣款商户号
     */
    private String deductionMerchantCode;

    /**
     * 签章渠道
     */
    private String signChannel;

    /**
     * 逾期短信发送方
     */
    private String overdueSmsSender;

    /**
     * 短信渠道
     */
    private String smsChannel;

    /**
     * 逾期宽限期类型 (SQ:首期, MQ:每期)
     */
    private String gracePeriodType;

    /**
     * 逾期宽限期(天)
     */
    private String gracePeriodDays;

    /**
     * 节假日是否顺延
     */
    private String holidayPostpone;

    /**
     * 征信查询方
     */
    private String creditQueryParty;

    /**
     * 征信上报方
     */
    private String creditReportSender;

    /**
     * 催收方
     */
    private String collectionParty;

    /**
     * 是否推送催收数据
     */
    private String pushCollectionData;

    /**
     * 是否支持催收减免
     */
    private String allowCollectionWaiver;

    /**
     * 版本号
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;
}
