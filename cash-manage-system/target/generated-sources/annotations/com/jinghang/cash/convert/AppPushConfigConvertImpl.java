package com.jinghang.cash.convert;

import com.jinghang.cash.enums.ClientNum;
import com.jinghang.cash.modules.manage.vo.req.ManagePusUpRequest;
import com.jinghang.cash.modules.manage.vo.req.ManagePushUpdateRequest;
import com.jinghang.cash.modules.manage.vo.res.ManageAppPushConfigQueryResponse;
import com.jinghang.cash.pojo.AppPushConfig;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-20T16:03:14+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
public class AppPushConfigConvertImpl implements AppPushConfigConvert {

    @Override
    public AppPushConfig toAppPushConfig(ManagePusUpRequest request) {
        if ( request == null ) {
            return null;
        }

        AppPushConfig appPushConfig = new AppPushConfig();

        if ( request.getClientNum() != null ) {
            appPushConfig.setClientNum( Enum.valueOf( ClientNum.class, request.getClientNum() ) );
        }
        appPushConfig.setVersionName( request.getVersionName() );
        appPushConfig.setVersionNum( request.getVersionNum() );
        appPushConfig.setPushType( request.getPushType() );
        if ( request.getPushEnable() != null ) {
            appPushConfig.setPushEnable( request.getPushEnable().name() );
        }
        appPushConfig.setPushContext( request.getPushContext() );
        appPushConfig.setPushUrl( request.getPushUrl() );

        return appPushConfig;
    }

    @Override
    public ManageAppPushConfigQueryResponse toManageAppPushConfigQueryResponse(AppPushConfig appPushConfig) {
        if ( appPushConfig == null ) {
            return null;
        }

        ManageAppPushConfigQueryResponse manageAppPushConfigQueryResponse = new ManageAppPushConfigQueryResponse();

        manageAppPushConfigQueryResponse.setClientNumId( appPushConfig.getId() );
        if ( appPushConfig.getClientNum() != null ) {
            manageAppPushConfigQueryResponse.setClientNum( appPushConfig.getClientNum().name() );
        }
        manageAppPushConfigQueryResponse.setVersionName( appPushConfig.getVersionName() );
        manageAppPushConfigQueryResponse.setVersionNum( appPushConfig.getVersionNum() );
        if ( appPushConfig.getPushType() != null ) {
            manageAppPushConfigQueryResponse.setPushType( appPushConfig.getPushType().name() );
        }
        manageAppPushConfigQueryResponse.setPushEnable( appPushConfig.getPushEnable() );
        manageAppPushConfigQueryResponse.setPushContext( appPushConfig.getPushContext() );
        manageAppPushConfigQueryResponse.setPushUrl( appPushConfig.getPushUrl() );
        manageAppPushConfigQueryResponse.setCreatedTime( appPushConfig.getCreatedTime() );
        manageAppPushConfigQueryResponse.setUpdatedTime( appPushConfig.getUpdatedTime() );

        return manageAppPushConfigQueryResponse;
    }

    @Override
    public AppPushConfig toAppPushConfig(ManagePushUpdateRequest request) {
        if ( request == null ) {
            return null;
        }

        AppPushConfig appPushConfig = new AppPushConfig();

        appPushConfig.setId( request.getClientNumId() );
        if ( request.getClientNum() != null ) {
            appPushConfig.setClientNum( Enum.valueOf( ClientNum.class, request.getClientNum() ) );
        }
        appPushConfig.setVersionName( request.getVersionName() );
        appPushConfig.setVersionNum( request.getVersionNum() );
        appPushConfig.setPushType( request.getPushType() );
        if ( request.getPushEnable() != null ) {
            appPushConfig.setPushEnable( request.getPushEnable().name() );
        }
        appPushConfig.setPushContext( request.getPushContext() );
        appPushConfig.setPushUrl( request.getPushUrl() );

        return appPushConfig;
    }
}
