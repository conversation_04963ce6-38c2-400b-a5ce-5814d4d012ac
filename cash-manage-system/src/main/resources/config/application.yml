spring:
  application:
    name: cash-flow-manage
logging:
  file:
    name: '/home/<USER>/logs/cash-manage/cash-manage.log'
  pattern:
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n'
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n'
  level:
    com.netflix.discovery: warn
    jdbc: off
app:
  id: cash-manage										# 应用唯一标识
apollo:
  autoUpdateInjectedSpringProperties: true        # 是否开启 Spring 参数自动更新
  bootstrap:
    enabled: true                                 # 是否开启 Apollo
    namespaces: application                       # 设置命名空间
    eagerLoad:
      enabled: true