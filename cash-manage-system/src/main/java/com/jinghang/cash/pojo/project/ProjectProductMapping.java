package com.jinghang.cash.pojo.project;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品项目代码映射
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:41
 */
@Getter
@Setter
@TableName("project_product_mapping")
public class ProjectProductMapping implements Serializable {

    @Serial
    private static final long serialVersionUID = 1234567890123456789L;

    /**
     * 产品编码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 项目类型名称
     */
    @TableField("project_code")
    private String projectCode;
}
