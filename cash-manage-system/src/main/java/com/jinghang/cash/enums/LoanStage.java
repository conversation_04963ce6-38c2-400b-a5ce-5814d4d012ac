package com.jinghang.cash.enums;

/**
 * <AUTHOR>
 * @date 2023/9/22
 */
public enum LoanStage {
    RISK, CREDIT, LOAN, LOAN_AFTER, REPAY;

    public static String toLoanStage(LoanStage channel) {
        return switch (channel) {
            case RISK -> "1";
            case CREDIT -> "2";
            case LOAN -> "3";
            case LOAN_AFTER -> "4";
            case REPAY -> "5";
            default -> "0";
        };
    }
}
