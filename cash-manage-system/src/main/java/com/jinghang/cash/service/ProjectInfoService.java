package com.jinghang.cash.service;

import com.jinghang.cash.api.dto.ProjectInfoDto;

/**
 * 项目信息服务接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:16
 */
public interface ProjectInfoService {

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    ProjectInfoDto queryProjectInfo(String projectCode);

    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    void clearProjectInfoCache(String projectCode);
}
