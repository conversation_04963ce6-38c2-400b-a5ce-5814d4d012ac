package com.jinghang.cash.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.pojo.project.ProjectInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 项目信息Mapper
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 13:40
 */
@Mapper
public interface ProjectInfoMapper extends BaseMapper<ProjectInfo> {

    /**
     * 根据项目编码查询项目信息
     *
     * @param projectCode 项目编码
     * @return 项目信息
     */
    ProjectInfo selectByProjectCode(@Param("projectCode") String projectCode);
}
