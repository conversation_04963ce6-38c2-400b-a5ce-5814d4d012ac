package com.jinghang.cash.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.pojo.project.ProjectElementsExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 项目要素扩展Mapper
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 13:38
 */
@Mapper
public interface ProjectElementsExtMapper extends BaseMapper<ProjectElementsExt> {

    /**
     * 根据项目编码查询项目要素扩展
     *
     * @param projectCode 项目编码
     * @return 项目要素扩展
     */
    ProjectElementsExt selectByProjectCode(@Param("projectCode") String projectCode);
}
