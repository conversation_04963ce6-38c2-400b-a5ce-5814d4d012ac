package com.jinghang.cash.modules.manage.controller;

import com.jinghang.cash.annotation.AnonymousAccess;
import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.service.ProjectInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 项目信息管理
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 11:43
 */
@RestController
@RequestMapping("/projectInfo")
public class ProjectInfoController {

    @Autowired
    private ProjectInfoService projectInfoService;

    @PostMapping(value = "/query/{projectCode}")
    @ApiOperation("根据项目编码查询项目信息")
    @AnonymousAccess
    public RestResult<ProjectInfoDto> queryProjectInfo(@PathVariable String projectCode) {
        ProjectInfoDto projectInfo = projectInfoService.queryProjectInfo(projectCode);
        return RestResult.success(projectInfo);
    }
}
