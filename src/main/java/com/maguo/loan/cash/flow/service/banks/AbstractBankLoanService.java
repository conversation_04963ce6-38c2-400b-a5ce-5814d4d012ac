package com.maguo.loan.cash.flow.service.banks;

import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.maguo.loan.cash.flow.entity.Loan;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/30
 */
public abstract class AbstractBankLoanService implements IBankService {
    public abstract BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt,BigDecimal SAN_LIU_LING,long num);
}
