package com.maguo.loan.cash.flow.service.push;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.maguo.loan.cash.flow.dto.pushmk.TokenResponseData;
import com.maguo.loan.cash.flow.entity.ShmPushFailFlow;
import com.maguo.loan.cash.flow.exception.ShmBusinessException;
import com.maguo.loan.cash.flow.repository.ShmPushFailFlowRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public abstract class AbstractPushToMkService implements PushService {

    private static final Logger log = LoggerFactory.getLogger(AbstractPushToMkService.class);

    @Value("${shm.push.token.url}")
    private String tokenUrl;

    @Value("${shm.push.token.userName}")
    private String userName;

    @Value("${shm.push.token.passWord}")
    private String passWord;

    @Value("${shm.push.connectTimeout}")
    private int connectTimeout;

    @Value("${shm.push.socketTimeout}")
    private int socketTimeout;

    protected abstract String getBody(String request);

    protected abstract String getUrl();

    protected abstract String msgEncrypt(String msg);

    @Autowired
    private ShmPushFailFlowRepository shmPushFailFlowRepository;


    @Override
    public void execute(String loanId) {
        try {
            executeWithRetry(loanId, false);
        } catch (Exception e) {
            try {
                executeWithRetry(loanId, true);
            } catch (Exception ex) {
                log.error("执行异常", ex);
                insertInto(loanId, ex instanceof ShmBusinessException ? ex.getMessage() : "处理异常");
            }
        }
    }


    public void executeWithRetry(String loanId, boolean isRetry) {
        if (isRetry) {
            log.info("开始重试处理loanId: {}", loanId);
        }
        try {
            // 组装报文
            String reqMsg = initData(loanId);
            // 获取token
            String token = this.getToken();
            // 发送报文
            String respMsg = this.sendMsg(reqMsg, token, getUrl());
            // 处理响应
            this.responseDeal(respMsg);
        } catch (Exception e) {
            // 记录是否是重试的错误日志
            if (isRetry) {
                log.error("重试处理loanId: {} 失败", loanId, e);
            }
            throw e;
        }
    }

    @Override
    public String initData(String request) {
        // 组装调用报文
        String body = getBody(request);
        // des加密
        String msgEncrypt = msgEncrypt(body);
        // 密后报文
        JSONObject requestJson = new JSONObject();
        requestJson.put("txt", msgEncrypt);
        return requestJson.toJSONString();
    }


    @Override
    public String sendMsg(String requestString, String token, String url) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            StringEntity requestEntity = new StringEntity(requestString, "UTF-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setEntity(requestEntity);
            httpPost.setHeader(HttpHeaders.AUTHORIZATION, "Bearer " + token);
            httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
            httpPost.setHeader(HttpHeaders.ACCEPT, "application/json");

            // 配置请求超时参数
            RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(connectTimeout)         // 连接超时时间
                .setSocketTimeout(socketTimeout)           // 数据读取超时时间
                .build();
            httpPost.setConfig(config);
            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                if (entity == null) {
                    throw new ShmBusinessException("响应实体为空");
                }
                String responseStr = EntityUtils.toString(entity, "UTF-8");
                log.info("responseStr:{}", responseStr);
                return responseStr;
            }
        } catch (Exception e) {
            log.error("调用接口异常", e);
            throw new ShmBusinessException("调用新增客户资料接口异常");
        }
    }

    @Override
    public void responseDeal(String response) {

        if (ObjectUtils.isEmpty(response)) {
            throw new ShmBusinessException("响应为空");
        }
        JSONObject responseObj = JSON.parseObject(response);

        // 从响应中提取状态码
        Integer code = responseObj.getInteger("code");
        String message = responseObj.getString("message");

        // 判断响应状态
        if (code == 0) {
            log.info("请求处理成功");
        } else {
            log.error("请求处理返回错误，状态码{},错误信息{}", code, message);
            throw new ShmBusinessException("请求处理返回错误: " + message);
        }
    }

    @Override
    public String getToken() {
        String fullUrl = String.format("%s?username=%s&password=%s",
            tokenUrl,
            URLEncoder.encode(userName, StandardCharsets.UTF_8),
            URLEncoder.encode(passWord, StandardCharsets.UTF_8));

        HttpGet httpGet = new HttpGet(fullUrl);
        RequestConfig config = RequestConfig.custom()
            .setConnectTimeout(connectTimeout)         // 连接超时时间
            .setSocketTimeout(socketTimeout)           // 数据读取超时时间
            .build();
        httpGet.setConfig(config);

        String responseStr;
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(httpGet)) {

            if (response.getStatusLine().getStatusCode() != 200) {
                throw new ShmBusinessException("调用异常: " + response.getStatusLine().getStatusCode());
            }

            // 获取响应实体
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                throw new ShmBusinessException("响应实体为空");
            }
            responseStr = EntityUtils.toString(entity, "UTF-8");
            log.info("获取token响应: {}", responseStr);
        } catch (Exception e) {
            log.error("获取token异常", e);
            throw new ShmBusinessException("获取token异常");
        }

        TokenResponseData tokenResponseData = parseResponse(responseStr);
        return tokenResponseData.getToken();
    }

    private static TokenResponseData parseResponse(String jsonResponse) {
        Gson gson = new Gson();
        JsonObject jsonObject = gson.fromJson(jsonResponse, JsonObject.class);
        int code = jsonObject.get("code").getAsInt();
        if (code != 0) {
            throw new ShmBusinessException("请求失败: " +
                jsonObject.get("message").getAsString());
        }
        JsonObject data = jsonObject.getAsJsonObject("data");

        return new TokenResponseData(
            data.get("token").getAsString(),
            data.get("expire").getAsInt()
        );
    }


    private void insertInto(String loanId, String message) {
        ShmPushFailFlow shmPushFailFlow = new ShmPushFailFlow();
        shmPushFailFlow.setLoanId(loanId);
        shmPushFailFlow.setFailMsg(message);
        shmPushFailFlowRepository.save(shmPushFailFlow);
    }
}
