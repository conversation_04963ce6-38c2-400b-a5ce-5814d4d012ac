package com.maguo.loan.cash.flow.entrance.common.dto.request.callback;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.entrance.common.dto.request.base.CommonApiRequest;
import com.maguo.loan.cash.flow.entrance.common.enums.api.CallBackApi;


import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 还款结果推送
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
public class RepaidPushReqDTO implements CommonApiRequest {
    /**
     * 合作机构单号
     */
    private String partnerOrderNo;
    /**
     * 我方单号
     */
    private String orderId;

    /**
     * 我方还款流水号
     */
    private String repayNo;
    /**
     * 合作机构还款流水号
     */
    private String partnerRepayNo;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 还款目的
     */
    private String repayPurpose;
    /**
     * 还款模式,线上线下
     */
    private String repayMode;
    /**
     * 实还款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime repaidDate;
    /**
     * 实还款本金
     */
    private BigDecimal actPrincipalAmt;
    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt;
    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;
    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeAmt;
    /**
     * 实还咨询费
     */
    private BigDecimal actConsultFee;
    /**
     * 实还违约金
     */
    private BigDecimal actBreachAmt;
    /**
     * 实还总额
     */
    private BigDecimal actAmount;
    /**
     * 剩余本金
     */
    private BigDecimal remainPrincipalAmt;
    /**
     * 剩余利息
     */
    private BigDecimal remainInterestAmt;
    /**
     * 剩余罚息
     */
    private BigDecimal remainPenaltyAmt;
    /**
     * 剩余担保费
     */
    private BigDecimal remainGuaranteeAmt;
    /**
     * 剩余咨询费
     */
    private BigDecimal remainConsultFee;
    /**
     * 剩余违约金
     */
    private BigDecimal remainBreachAmt = BigDecimal.ZERO;
    /**
     * 剩余待还总额
     */
    private BigDecimal remainAmount;
    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    private String repayResult;

    public String getRepayResult() {
        return repayResult;
    }

    public void setRepayResult(String repayResult) {
        this.repayResult = repayResult;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPartnerRepayNo() {
        return partnerRepayNo;
    }

    public void setPartnerRepayNo(String partnerRepayNo) {
        this.partnerRepayNo = partnerRepayNo;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(String repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public LocalDateTime getRepaidDate() {
        return repaidDate;
    }

    public void setRepaidDate(LocalDateTime repaidDate) {
        this.repaidDate = repaidDate;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActGuaranteeAmt() {
        return actGuaranteeAmt;
    }

    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }

    public BigDecimal getActConsultFee() {
        return actConsultFee;
    }

    public void setActConsultFee(BigDecimal actConsultFee) {
        this.actConsultFee = actConsultFee;
    }

    public BigDecimal getActBreachAmt() {
        return actBreachAmt;
    }

    public void setActBreachAmt(BigDecimal actBreachAmt) {
        this.actBreachAmt = actBreachAmt;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public BigDecimal getRemainPrincipalAmt() {
        return remainPrincipalAmt;
    }

    public void setRemainPrincipalAmt(BigDecimal remainPrincipalAmt) {
        this.remainPrincipalAmt = remainPrincipalAmt;
    }

    public BigDecimal getRemainInterestAmt() {
        return remainInterestAmt;
    }

    public void setRemainInterestAmt(BigDecimal remainInterestAmt) {
        this.remainInterestAmt = remainInterestAmt;
    }

    public BigDecimal getRemainPenaltyAmt() {
        return remainPenaltyAmt;
    }

    public void setRemainPenaltyAmt(BigDecimal remainPenaltyAmt) {
        this.remainPenaltyAmt = remainPenaltyAmt;
    }

    public BigDecimal getRemainGuaranteeAmt() {
        return remainGuaranteeAmt;
    }

    public void setRemainGuaranteeAmt(BigDecimal remainGuaranteeAmt) {
        this.remainGuaranteeAmt = remainGuaranteeAmt;
    }

    public BigDecimal getRemainConsultFee() {
        return remainConsultFee;
    }

    public void setRemainConsultFee(BigDecimal remainConsultFee) {
        this.remainConsultFee = remainConsultFee;
    }

    public BigDecimal getRemainBreachAmt() {
        return remainBreachAmt;
    }

    public void setRemainBreachAmt(BigDecimal remainBreachAmt) {
        this.remainBreachAmt = remainBreachAmt;
    }

    public BigDecimal getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(BigDecimal remainAmount) {
        this.remainAmount = remainAmount;
    }

    @Override
    public CallBackApi getApiType() {
        return CallBackApi.REPAID_PUSH;
    }


}
