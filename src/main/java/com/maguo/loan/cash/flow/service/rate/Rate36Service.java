package com.maguo.loan.cash.flow.service.rate;


import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.banks.BankManager;
import com.maguo.loan.cash.flow.service.flow.FlowManager;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class Rate36Service implements IRateService {

    public static final BigDecimal SAN_LIU_LING = new BigDecimal("360");

    @Override
    public RateLevel rateLevel() {
        return RateLevel.RATE_36;
    }

    @Autowired
    private BankManager bankManager;
    @Autowired
    private FlowManager flowManager;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Override
    public BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt, long num) {
        return flowManager.getFlowLoanService(loan.getFlowChannel()).planConsultFee(loan, planItemDto, remainingPrincipalAmt, SAN_LIU_LING, num);
    }


    @Override
    public void trialClearSetting(Loan loan, TrialResultVo trialResultVo, List<RepayPlan> repayPlans) {
        switch (loan.getBankChannel()) {
            default -> {
                trialResultVo.setGuaranteeFee(trialResultVo.getCapitalGuaranteeFee());
                trialResultVo.setExtraGuaranteeFee(BigDecimal.ZERO);
                // 咨询费累加
                trialResultVo.setConsultFee(repayPlans.stream().map(RepayPlan::getConsultFee).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
    }


    /**
     * 试算结清咨询费
     *
     * @param loan       借据
     * @param period     期数
     * @param repayPlans 还款计划
     * @return 应还咨询费
     */
    public BigDecimal trialClearGuaranteeAmt(Loan loan, Integer period, List<RepayPlan> repayPlans, LocalDate date) {
        BigDecimal consultAmt;
        //融担费费率
        QhBank qhBank = QhBank.getQhBankBy(loan.getBankChannel());
        BigDecimal guaranteeRate = qhBank.getBankCustomRate().subtract(qhBank.getBankRate());

        if (period == 1) {
            //首期结清
            consultAmt = loan.getAmount().multiply(guaranteeRate)
                .multiply(new BigDecimal(DateUtil.dateDiff(loan.getLoanTime().toLocalDate(), date))).divide(SAN_LIU_LING, 2, RoundingMode.HALF_UP);
        } else {
            //上一期账单日
            RepayPlan priorPlan = repayPlanRepository.findByLoanIdAndPeriod(loan.getId(), period - 1);

            //非首期结清
            consultAmt = repayPlans.stream().filter(s -> s.getPeriod() >= period).map(RepayPlan::getPrincipalAmt).reduce(BigDecimal.ZERO, BigDecimal::add)
                .multiply(guaranteeRate).multiply(new BigDecimal(DateUtil.dateDiff(priorPlan.getPlanRepayDate(), date)))
                .divide(SAN_LIU_LING, 2, RoundingMode.HALF_UP);
        }

        return consultAmt;
    }
}
