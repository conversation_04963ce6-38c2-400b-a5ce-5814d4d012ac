package com.maguo.loan.cash.flow.service.client;

import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.remote.riskdata.req.RiskDataRequest;
import com.maguo.loan.cash.flow.remote.riskdata.resp.RiskDataResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;


/**
* <AUTHOR>
* @Description 内部风控系统客户端
* @Date 2025-05-23
* @Version 1.0
*/
@Service
public class RiskControlClient {
    private static final Logger logger = LoggerFactory.getLogger(RiskControlClient.class);

    private final WebClient webClient;

    @Autowired
    public RiskControlClient(
        WebClient.Builder webClientBuilder,
        @Value("${risk.control.endpoint}") String endpoint
    ) {
        logger.info("风控调用url : [{}]", endpoint);
        this.webClient = webClientBuilder
            .baseUrl(endpoint)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .build();
    }

    //@Value("${risk.control.endpoint}")
    //private String endpoint;

    //private final RestTemplate restTemplate;

    //@Autowired
    //public RiskControlClient(RestTemplate restTemplate) {
    //    this.restTemplate = restTemplate;
    //}

    ///**
    // * 执行风控检查（同步调用）
    // */
    //public RiskDataResponse evaluate(RiskDataRequest request) {
    //    try {
    //        return restTemplate.postForObject(
    //            endpoint + "/de/apiCall/executeFlowCall",
    //            request,
    //            RiskDataResponse.class
    //        );
    //    } catch (RestClientException e) {
    //        logger.error("风控请求失败: [{}]", request, e);
    //        throw new BizException(ResultCode.RISK_CONNECT_ERROR);
    //    }
    //}

    /**
     * 执行风控检查（同步调用）
     */
    public RiskDataResponse evaluate(RiskDataRequest request) {
        try {
            return webClient.post()
                .uri("/de/apiCall/executeFlowCall")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(RiskDataResponse.class)
                .block(); // 同步阻塞调用
        } catch (WebClientResponseException e) {
            logger.error("风控请求失败 - 状态码: [{}], 响应: [{}]", e.getStatusCode(), e.getResponseBodyAsString(), e);
            throw new BizException(ResultCode.RISK_CONNECT_ERROR);
        } catch (Exception e) {
            logger.error("风控请求失败: [{}]", request, e);
            throw new BizException(ResultCode.RISK_CONNECT_ERROR);
        }
    }



    /**
     * 异步版本（推荐）
     */
    public Mono<RiskDataResponse> evaluateAsync(RiskDataRequest request) {
        return webClient.post()
            .uri("/de/apiCall/executeFlowCall")
            .bodyValue(request)
            .retrieve()
            .bodyToMono(RiskDataResponse.class)
            .onErrorMap(e -> {
                logger.error("风控请求失败", e);
                return new BizException(ResultCode.RISK_CONNECT_ERROR);
            });
    }
}
