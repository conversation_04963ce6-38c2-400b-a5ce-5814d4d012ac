package com.maguo.loan.cash.flow.entrance.common.dto.response.bankCard;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
public class BankListResDTO {

    private List<BankDTO> bankCardList;

    public static class BankDTO {
        /**
         * 银行名字
         */
        private String bankName;
        /**
         * 中文简写
         */
        private String bankShortName;
        /**
         * 英文缩写
         */
        private String bankAbbr;
        /**
         * 旧英文缩写
         */
        private String bankOldAbbr;
        /**
         * 图标
         */
        private String icon;


        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getBankShortName() {
            return bankShortName;
        }

        public void setBankShortName(String bankShortName) {
            this.bankShortName = bankShortName;
        }

        public String getBankAbbr() {
            return bankAbbr;
        }

        public void setBankAbbr(String bankAbbr) {
            this.bankAbbr = bankAbbr;
        }

        public String getBankOldAbbr() {
            return bankOldAbbr;
        }

        public void setBankOldAbbr(String bankOldAbbr) {
            this.bankOldAbbr = bankOldAbbr;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }
    }

    public List<BankDTO> getBankCardList() {
        return bankCardList;
    }

    public void setBankCardList(List<BankDTO> bankCardList) {
        this.bankCardList = bankCardList;
    }
}
