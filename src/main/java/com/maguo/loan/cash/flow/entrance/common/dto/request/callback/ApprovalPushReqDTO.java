package com.maguo.loan.cash.flow.entrance.common.dto.request.callback;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.entrance.common.dto.request.base.CommonApiRequest;
import com.maguo.loan.cash.flow.entrance.common.enums.api.CallBackApi;


import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 进件(风控)审批回调
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
public class ApprovalPushReqDTO implements CommonApiRequest {
    /**
     * 合作机构单号
     */
    private String partnerOrderNo;
    /**
     * 审核状态
     */
    private String approvalStatus;
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approvalTime;
    /**
     * 审核金额
     */
    private BigDecimal amount;
    /**
     * 我方单号
     */
    private String orderId;
    /**
     * 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;
    /**
     * 失败后可以在此申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime freeTime;

    /**
     * 对客利率
     */
    private BigDecimal approvalRate;

    public BigDecimal getApprovalRate() {
        return approvalRate;
    }

    public void setApprovalRate(BigDecimal approvalRate) {
        this.approvalRate = approvalRate;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public LocalDateTime getFreeTime() {
        return freeTime;
    }

    public void setFreeTime(LocalDateTime freeTime) {
        this.freeTime = freeTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public LocalDateTime getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(LocalDateTime approvalTime) {
        this.approvalTime = approvalTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public CallBackApi getApiType() {
        return CallBackApi.APPROVAL_PUSH;
    }
}
