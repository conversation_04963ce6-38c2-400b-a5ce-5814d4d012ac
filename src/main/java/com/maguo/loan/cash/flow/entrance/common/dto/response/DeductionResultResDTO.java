package com.maguo.loan.cash.flow.entrance.common.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 权益扣费结果
 *
 * <AUTHOR>
 */
public class DeductionResultResDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 284079733025508498L;

    /**
     * 合作机构单号
     */
    private String partnerOrderNo;

    /**
     * 我方单号
     */
    private String orderId;
    /**
     * 权益金额
     */
    private BigDecimal rightsAmount;

    /**
     * 权益应还时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate shouldRepayDate;

    /**
     * 权益实还时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime actualRepayTime;

    /**
     * 权益扣款状态
     */
    private String rightsRepayStatus;


    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getRightsAmount() {
        return rightsAmount;
    }


    public void setRightsAmount(BigDecimal rightsAmount) {
        this.rightsAmount = rightsAmount;
    }

    public LocalDate getShouldRepayDate() {
        return shouldRepayDate;
    }

    public void setShouldRepayDate(LocalDate shouldRepayDate) {
        this.shouldRepayDate = shouldRepayDate;
    }

    public LocalDateTime getActualRepayTime() {
        return actualRepayTime;
    }

    public void setActualRepayTime(LocalDateTime actualRepayTime) {
        this.actualRepayTime = actualRepayTime;
    }

    public String getRightsRepayStatus() {
        return rightsRepayStatus;
    }

    public void setRightsRepayStatus(String rightsRepayStatus) {
        this.rightsRepayStatus = rightsRepayStatus;
    }
}
