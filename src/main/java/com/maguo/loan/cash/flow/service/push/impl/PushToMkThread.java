package com.maguo.loan.cash.flow.service.push.impl;

import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class PushToMkThread {
    private static final Logger log = LoggerFactory.getLogger(PushToMkThread.class);
    @Resource
    private PushToMkServiceImpl pushToMkService;

    @Async("shmPush")
    public void shmPush(String loanId) {
        log.info("处理推送客户信息至上海脉客诉系统-----开始-----loanId：{}", loanId);
        pushToMkService.execute(loanId);
        log.info("处理推送客户信息至上海脉客诉系统-----完成-----loanId：{}", loanId);

    }

}
