package com.maguo.loan.cash.flow.service;

import com.maguo.loan.cash.flow.common.CallBackException;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
public abstract class AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(AbstractListener.class);
    protected static final int MAX_RETRY_TIMES = 3;
    protected static final String TRY_TIMES_KEY = "MQ_RETRY_TIMES";

    private final MqService mqService;
    private final WarningService mqWarningService;

    public AbstractListener(MqService mqService, WarningService mqWarningService) {
        this.mqService = mqService;
        this.mqWarningService = mqWarningService;
    }


    public MqService getMqService() {
        return mqService;
    }

    public WarningService getMqWarningService() {
        return mqWarningService;
    }

    protected boolean isExceedMaxRetry(int retryTimes) {
        return retryTimes >= MAX_RETRY_TIMES;
    }

    protected int retriedTimes(Message message) {
        Object times = message.getMessageProperties().getHeaders().get(TRY_TIMES_KEY);
        return times == null ? 1 : (int) times + 1;
    }

    protected void processException(String body, Message message, Exception e, String desc, BiConsumer<String, Map<String, Object>> retry) {
        int retryTimes = retriedTimes(message);
        if (retryTimes == 1 && !(e instanceof CallBackException )) {
            // 首次预警
            mqWarningService.warn(body + "," + desc + "," + e.getMessage(), msg -> logger.error(msg, e));
        }
        if (isExceedMaxRetry(retryTimes)) {
            mqWarningService.warn(
                "[" + retryTimes + "][" + body + "]" + desc + "，超过最大重试次数，不再重试:" + e.getMessage(),
                msg -> logger.error(msg, e));
            return;
        }
        Map<String, Object> headers = new HashMap<>();
        headers.put(TRY_TIMES_KEY, retryTimes);
        retry.accept(body, headers);
    }

    protected void ackMsg(String body, Message message, Channel channel) {
        try {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (IOException e) {
            logger.error("[{}][{}]消息确认异常", body, message.getMessageProperties().getConsumerQueue(), e);
        }
    }
}
