package com.maguo.loan.cash.flow.entrance.common.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 借款试算返回结构
 *
 * <AUTHOR>
 */
public class TrialResDTO {

    /**
     * 期次
     */
    private Integer applyPeriods;
    /**
     * 额度
     */
    private BigDecimal approveAmount;
    /**
     * 利率
     */
    private BigDecimal rate;

    /**
     * 试算还款计划
     */
    private List<Plan> plans;

    /**
     * 试算生成还款计划
     */
    public static class Plan {

        /**
         * 期次
         */
        private Integer period;
        /**
         * 计划还款日
         */
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private LocalDate planRepayDate;
        /**
         * 应还总额
         */
        private BigDecimal totalAmount;
        /**
         * 应还本金
         */
        private BigDecimal principalAmt;
        /**
         * 应还利息
         */
        private BigDecimal interestAmt;
        /**
         * 应还担保费
         */
        private BigDecimal guaranteeAmt;
        /**
         * 应还咨询费
         */
        private BigDecimal consultFee;

        public BigDecimal getInterestAmt() {
            return interestAmt;
        }

        public void setInterestAmt(BigDecimal interestAmt) {
            this.interestAmt = interestAmt;
        }

        public Integer getPeriod() {
            return period;
        }

        public void setPeriod(Integer period) {
            this.period = period;
        }

        public LocalDate getPlanRepayDate() {
            return planRepayDate;
        }

        public void setPlanRepayDate(LocalDate planRepayDate) {
            this.planRepayDate = planRepayDate;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public void setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
        }

        public BigDecimal getPrincipalAmt() {
            return principalAmt;
        }

        public void setPrincipalAmt(BigDecimal principalAmt) {
            this.principalAmt = principalAmt;
        }

        public BigDecimal getGuaranteeAmt() {
            return guaranteeAmt;
        }

        public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
            this.guaranteeAmt = guaranteeAmt;
        }

        public BigDecimal getConsultFee() {
            return consultFee;
        }

        public void setConsultFee(BigDecimal consultFee) {
            this.consultFee = consultFee;
        }
    }

    public Integer getApplyPeriods() {
        return applyPeriods;
    }

    public void setApplyPeriods(Integer applyPeriods) {
        this.applyPeriods = applyPeriods;
    }

    public BigDecimal getApproveAmount() {
        return approveAmount;
    }

    public void setApproveAmount(BigDecimal approveAmount) {
        this.approveAmount = approveAmount;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public List<Plan> getPlans() {
        return plans;
    }

    public void setPlans(List<Plan> plans) {
        this.plans = plans;
    }
}
