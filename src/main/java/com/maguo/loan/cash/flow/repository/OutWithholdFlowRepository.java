package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.OutWithholdFlow;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/8/7 13:49
 */
public interface OutWithholdFlowRepository extends JpaRepository<OutWithholdFlow, String> {

    Optional<OutWithholdFlow> findByLoanIdAndPeriodAndPayState(String loanId, Integer period, ProcessState payState);

    OutWithholdFlow findByRepayRecordId(String repayRecordId);

}
