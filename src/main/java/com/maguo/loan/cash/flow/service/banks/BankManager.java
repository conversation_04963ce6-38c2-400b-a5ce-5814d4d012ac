package com.maguo.loan.cash.flow.service.banks;

import com.alibaba.fastjson.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/30
 */
@Service
public class BankManager {

    private static final Logger logger = LoggerFactory.getLogger(BankManager.class);

    private Map<BankChannel, AbstractBankLoanService> bankLoanServices = new HashMap<>();
    private Map<BankChannel, AbstractBankRepayService> bankRepayServices = new HashMap<>();

    public AbstractBankLoanService getBankLoanService(BankChannel bankChannel) {
        return Optional.ofNullable(bankLoanServices.get(bankChannel)).orElseThrow();
    }

    public AbstractBankRepayService getBankRepayService(BankChannel bankChannel) {
        return Optional.ofNullable(bankRepayServices.get(bankChannel)).orElseThrow();
    }


    @Autowired
    public void setBankLoanServices(List<AbstractBankLoanService> bankLoanServices) {
        this.bankLoanServices = bankLoanServices.stream().collect(Collectors.toMap(IBankService::bankChannel, s -> s));
    }

    @Autowired
    public void setBankRepayServices(List<AbstractBankRepayService> bankRepayServices) {
        logger.info("!!!!!!!!  bankRepayServices: [{}]", JSON.toJSONString(bankRepayServices));
        this.bankRepayServices = bankRepayServices.stream().collect(Collectors.toMap(IBankService::bankChannel, s -> s));
    }
}
