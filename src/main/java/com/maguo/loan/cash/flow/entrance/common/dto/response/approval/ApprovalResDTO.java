package com.maguo.loan.cash.flow.entrance.common.dto.response.approval;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 进件结果
 *
 * <AUTHOR>
 * @date 2024/8/20
 */
public class ApprovalResDTO {

    /**
     * 进件结果
     */
    private String approvalStatus;
    /**
     * 合作机构单号
     */
    private String partnerOrderNo;

    /**
     * 期数
     */
    private Integer period;
    /**
     * 审批金额
     */
    private BigDecimal amount;
    /**
     * 我方单号
     */
    private String orderId;

    /**
     * 拒绝原因
     */
    private String rejectReason;
    /**
     * 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;
    /**
     * 失败后可以在此申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime freeTime;

    /**
     * 是否可以购买权益
     */
    private Boolean rightsSwitch;
    /**
     * 审批利率
     */
    private BigDecimal approvalRate;

    public BigDecimal getApprovalRate() {
        return approvalRate;
    }

    public void setApprovalRate(BigDecimal approvalRate) {
        this.approvalRate = approvalRate;
    }

    public Boolean getRightsSwitch() {
        return rightsSwitch;
    }

    public void setRightsSwitch(Boolean rightsSwitch) {
        this.rightsSwitch = rightsSwitch;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public LocalDateTime getFreeTime() {
        return freeTime;
    }

    public void setFreeTime(LocalDateTime freeTime) {
        this.freeTime = freeTime;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
}
