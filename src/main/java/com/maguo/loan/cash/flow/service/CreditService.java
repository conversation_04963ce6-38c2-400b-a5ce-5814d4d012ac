package com.maguo.loan.cash.flow.service;


import com.alibaba.fastjson.JSONObject;
import cn.com.antcloud.api.AntFinTechApiClient;
import cn.com.antcloud.api.AntFinTechProfile;
import cn.com.antcloud.api.antcloud.AntCloudClientRequest;
import cn.com.antcloud.api.antcloud.AntCloudClientResponse;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.Relation;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.credit.BankCardInfoDto;
import com.jinghang.capital.api.dto.credit.CreditApplyDto;
import com.jinghang.capital.api.dto.credit.CreditQueryDto;
import com.jinghang.capital.api.dto.credit.CreditResultDto;
import com.jinghang.capital.api.dto.credit.ExtInfoDto;
import com.jinghang.capital.api.dto.credit.IdCardInfoDto;
import com.jinghang.capital.api.dto.credit.RecreditApplyDto;
import com.jinghang.capital.api.dto.credit.UserContactInfoDto;
import com.jinghang.capital.api.dto.credit.UserInfoDto;
import com.jinghang.capital.api.dto.credit.banks.cybk.CybkExtInfoDto;
import com.jinghang.capital.api.dto.file.FileInfoDto;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.CardConvert;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.convert.UserInfoConvert;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.CreditUserContactInfo;
import com.maguo.loan.cash.flow.entity.CreditUserInfo;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserDevice;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserPreScreeningQuery;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entrance.lvxin.config.MAYIConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.Position;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.remote.core.FinCreditService;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.CreditUserContactInfoRepository;
import com.maguo.loan.cash.flow.repository.CreditUserInfoRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserContactInfoRepository;
import com.maguo.loan.cash.flow.repository.UserDeviceRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserPreScreeningQueryRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.service.common.loan.LoanCommonService;
import com.maguo.loan.cash.flow.service.event.LoanResultEvent;
import com.maguo.loan.cash.flow.util.BaseConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/10/9
 */
@Service
public class CreditService {
    private static final Logger logger = LoggerFactory.getLogger(CreditService.class);

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private CreditUserInfoRepository creditUserInfoRepository;

    @Autowired
    private CreditUserContactInfoRepository creditUserContactInfoRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private UserOcrRepository userOcrRepository;

    @Autowired
    private UserFaceRepository userFaceRepository;

    @Autowired
    private UserDeviceRepository userDeviceRepository;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private UserContactInfoRepository userContactInfoRepository;

    @Autowired
    private FinCreditService finCreditService;

    @Autowired
    private MqService mqService;

    @Autowired
    private AgreementService agreementService;

    @Autowired
    private UserFileService userFileService;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private LoanCommonService loanCommonService;

    @Autowired
    private CapitalConfigRepository capitalConfigRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private UserPreScreeningQueryRepository userPreScreeningQueryRepository;
    @Autowired
    PreOrderRepository preOrderRepository;
    @Autowired
    private OrderCardService orderCardService;
    @Autowired
    private MAYIConfig mayiConfig;


    public void apply(Order order, String routerId) {
        // 落库
        Credit credit = initCredit(order, routerId);
        // 协议签署
        agreementService.applySign(credit.getOrderId(), LoanStage.CREDIT, order.getProjectCode());

        // 异步去core授信
        mqService.submitCreditApply(credit.getId());
    }

    private Credit initCredit(Order order, String routerId) {
        logger.info("初始化授信:{}", order.getId());
        Credit credit = new Credit();
        credit.setProjectCode(order.getProjectCode());
        credit.setRouterRecordId(routerId);
        credit.setUserId(order.getUserId());
        credit.setOrderId(order.getId());
        credit.setOuterCreditId(order.getOuterOrderId());
        //用户卡信息
        UserBankCard userBankCard = orderCardService.getCreditCard(order, order.getBankChannel());
        credit.setLoanCardId(userBankCard.getId());
        //风控表的金额为原始授信金额
//        UserRiskRecord riskRecord = userRiskRecordRepository.findById(order.getRiskId()).orElseThrow();
//        if (order.getAmountType() == AmountType.REVOLVING) {
//            credit.setCreditAmt(order.getApproveAmount());
//        } else {
//            //credit.setCreditAmt(riskRecord.getApproveAmount());
//            credit.setCreditAmt(new BigDecimal("1000.00"));
//        }
        //使用订单申请金额
        credit.setCreditAmt(order.getApplyAmount());
        credit.setIrrRate(order.getIrrRate());
        credit.setPeriods(order.getApplyPeriods());
        credit.setApplyTime(LocalDateTime.now());
        credit.setFlowChannel(order.getFlowChannel());
        credit.setLoanPurpose(order.getLoanPurpose());
        credit.setBankChannel(order.getBankChannel());

        //todo 分期乐调整获取融担公司，findByBankChannel可能会出现多条记录，需关注是否影像其他流量
        String guaranteeCompany = capitalConfigRepository.findGuaranteeCompanyByBankChannelAndFlowChannel(order.getBankChannel(),order.getFlowChannel());
        credit.setGuaranteeCompany(GuaranteeCompany.of(guaranteeCompany));
//            CapitalConfig capitalConfig = capitalConfigRepository.findByBankChannel(order.getBankChannel()).orElseThrow();
//            credit.setGuaranteeCompany(capitalConfig.getGuaranteeCompany());
        credit.setState(ProcessState.INIT);
        credit = creditRepository.save(credit);
        // 复制用户信息
        copyUserInfo(order, credit, userBankCard);
        return credit;
    }


    private void copyUserInfo(Order order, Credit credit, UserBankCard userBankCard) {
        // 基本信息
        UserInfo userInfo = userInfoRepository.findById(order.getUserId()).orElseThrow(() -> new BizException(ResultCode.USER_NOT_EXIST));
        CreditUserInfo creditUserInfo = UserInfoConvert.INSTANCE.toCreditUserInfo(userInfo);
        creditUserInfo.setUserId(order.getUserId());
        creditUserInfo.setCreditId(credit.getId());
        // ocr
        UserOcr userOcr = userOcrRepository.findByUserId(order.getUserId());
        creditUserInfo.setCertAddress(userOcr.getCertAddress());
        creditUserInfo.setCertSignOrg(userOcr.getCertSignOrg());
        creditUserInfo.setCertValidStart(userOcr.getCertValidStart());
        //如果身份证有效期截止时间超过2099-01-01，则设置为2099-01-01
        creditUserInfo.setCertValidEnd(userOcr.getCertValidEnd().isAfter(BaseConstants.DEFAULT_LONG_CERT_END)
                ? BaseConstants.DEFAULT_LONG_CERT_END : userOcr.getCertValidEnd());
        creditUserInfo.setHeadOssBucket(userOcr.getHeadOssBucket());
        creditUserInfo.setHeadOssKey(userOcr.getHeadOssKey());
        creditUserInfo.setNationOssBucket(userOcr.getNationOssBucket());
        creditUserInfo.setNationOssKey(userOcr.getNationOssKey());
        creditUserInfo.setCertProvinceCode(userOcr.getProvinceCode());
        creditUserInfo.setCertCityCode(userOcr.getCityCode());
        creditUserInfo.setCertDistrictCode(userOcr.getDistrictCode());
        creditUserInfo.setCertNation(userOcr.getNation());
        creditUserInfo.setCertGender(userOcr.getGender());
        // face
        UserFace userFace = userFaceRepository.findByUserId(order.getUserId());
        creditUserInfo.setFaceChannel(userFace.getFaceChannel());
        creditUserInfo.setFaceTime(userFace.getFaceTime());
        creditUserInfo.setFaceScore(userFace.getFaceScore());
        creditUserInfo.setFaceOssBucket(userFace.getOssBucket());
        creditUserInfo.setFaceOssKey(userFace.getOssKey());

        // card
        if (StringUtil.isNotBlank(order.getLoanCardId())) {
            creditUserInfo.setCardNo(userBankCard.getCardNo());
            creditUserInfo.setCardName(userBankCard.getCardName());
            creditUserInfo.setCardPhone(userBankCard.getPhone());
            creditUserInfo.setCardBankCode(userBankCard.getBankCode());
            creditUserInfo.setCardBankName(userBankCard.getBankName());
            creditUserInfo.setCardChannel(userBankCard.getChannel());
            creditUserInfo.setCardMerchantNo(userBankCard.getMerchantNo());
            creditUserInfo.setCardAgreeNo(userBankCard.getAgreeNo());
        }

        // device
        UserDevice userDevice = userDeviceRepository.findByUserId(order.getUserId());
        if (Objects.nonNull(userDevice)) {
            creditUserInfo.setDeviceModel(userDevice.getModel());
            creditUserInfo.setDeviceOsType(userDevice.getOsType());
            creditUserInfo.setDeviceOsVersion(userDevice.getOsVersion());
            creditUserInfo.setDeviceGps(userDevice.getGps());
            creditUserInfo.setDeviceIp(userDevice.getIp());
            creditUserInfo.setDeviceMac(userDevice.getMac());
        }
        // 客户评分
        UserRiskRecord userRiskRecord = userRiskRecordRepository.findById(order.getRiskId())
                .orElseThrow(() -> new BizException(ResultCode.RISK_RECORD_NOT_EXIST_ERROR));
        creditUserInfo.setAcardScore(userRiskRecord.getApproveScore());

        creditUserInfo = creditUserInfoRepository.save(creditUserInfo);
        // 拷贝联系人
        List<CreditUserContactInfo> creditUserContactInfos = new ArrayList<>();
        List<UserContactInfo> userContactInfos = userContactInfoRepository.queryByUserIdOrderByUpdatedTimeDesc(order.getUserId());
        if(FlowChannel.LVXIN.equals(credit.getFlowChannel())) {
            for (int i = 0; i < 2; i++) {
                CreditUserContactInfo creditUserContactInfo = UserInfoConvert.INSTANCE.toCreditUserContactInfo(userContactInfos.get(i));
                creditUserContactInfo.setCreditUserId(creditUserInfo.getId());
                creditUserContactInfos.add(creditUserContactInfo);
            }
        }else if(FlowChannel.PPCJDL.equals(credit.getFlowChannel())){
            for (int i = 0; i < userContactInfos.size(); i++) {
                CreditUserContactInfo creditUserContactInfo = UserInfoConvert.INSTANCE.toCreditUserContactInfo(userContactInfos.get(i));
                creditUserContactInfo.setCreditUserId(creditUserInfo.getId());
                creditUserContactInfos.add(creditUserContactInfo);
            }
        }
        creditUserContactInfoRepository.saveAll(creditUserContactInfos);
    }

    //调用蚂蚁撞库
    public void mayiAccess(String creditId) {
        PreOrder preOrder = preOrderRepository.findById(creditId).orElseThrow();
        AntFinTechProfile profile = AntFinTechProfile.getProfile(
            mayiConfig.getBaseUrl(),
            mayiConfig.getAccessKey(),
            mayiConfig.getAccessSecret());
        // 创建Client
        AntFinTechApiClient acApiClient = new AntFinTechApiClient(profile);
        // 创建Request
        AntCloudClientRequest request = new AntCloudClientRequest();
        // 指定调用方法
        request.setMethod("riskplus.dubbridge.router.userselect.query");
        // 指定版本号
        request.setVersion("1.0");
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("order_no",preOrder.getId());
        map.put("biz_type","1");
        map.put("custom_name",preOrder.getName());
        map.put("card_no",preOrder.getCertNo());
        map.put("mobile",preOrder.getMobile());
        map.put("platform_no","DIBPKJF");
        JSONObject object = new JSONObject(map);
        logger.info("调用外部接口蚂蚁前筛:{}", map);
        request.putParametersFromObject(object);
        String responseData = null;
        // 发送请求
        try {
            AntCloudClientResponse response = acApiClient.execute(request);
            // 获取返回值
            responseData = response.getData();
            logger.info("调用外部接口蚂蚁前筛返回结果:{}", responseData);
            UserPreScreeningQuery screeningQuery=new UserPreScreeningQuery();
            screeningQuery.setOrderNo(preOrder.getId());
            screeningQuery.setBizType("1");
            screeningQuery.setCustomName(preOrder.getName());
            screeningQuery.setCardNo(preOrder.getCertNo());
            screeningQuery.setMobile(preOrder.getMobile());
            screeningQuery.setUserClassifyInfo(responseData);
            userPreScreeningQueryRepository.save(screeningQuery);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void bankCredit(String creditId) {
        Credit credit = creditRepository.findById(creditId).orElseThrow();
        Order order = orderRepository.findById(credit.getOrderId()).orElseThrow();
        // 检查协议签署
        String projectCode = credit.getProjectCode();
        List<UserFile> userFiles = userFileService.checkMustCompleted(credit.getUserId(), order.getRiskId(), LoanStage.RISK,projectCode);
        if (Objects.isNull(userFiles)) {
            // 等待申请授信
            mqService.submitCreditApplyDelay(credit.getId());
            return;
        }
        // 去core授信
        logger.info("申请core授信:{}", creditId);
        RestResult<CreditResultDto> restResult = finCreditService.credit(wrapApplyCreditParam(credit, order, userFiles));
        logger.info("首次申请core授信结果:{}", JsonUtil.toJsonString(restResult));

        if (!restResult.isSuccess()) {
            // 更新失败原因
            credit.setFailReason(restResult.getMsg());
        } else {
            // 保存core授信编号
            CreditResultDto creditResultDto = restResult.getData();
            credit.setCreditNo(creditResultDto.getCreditId());
            credit.setState(ProcessState.PROCESSING);
        }
        creditRepository.save(credit);

        mqService.submitCreditResultQueryDelay(creditId);
    }

    private CreditApplyDto<ExtInfoDto> wrapApplyCreditParam(Credit credit, Order order, List<UserFile> creditFiles) {
        CreditApplyDto<ExtInfoDto> creditApplyDto = new CreditApplyDto<>();

        CreditUserInfo creditUserInfo = creditUserInfoRepository.findByCreditId(credit.getId())
                .orElseThrow(() -> new BizException(ResultCode.USER_INFO_NOT_EXIST));
        // 一些基本参数
        creditApplyDto.setSysId(credit.getId());
        creditApplyDto.setProduct(Product.ZC_CASH);
        creditApplyDto.setBankChannel(credit.getBankChannel());
        creditApplyDto.setFlowChannel(EnumConvert.INSTANCE.toCoreApi(credit.getFlowChannel()));
        creditApplyDto.setGuaranteeCompany(credit.getGuaranteeCompany());
        if (credit.getLoanPurpose() != null) {
            creditApplyDto.setProductItem(credit.getLoanPurpose().getDesc());
        }
        // 授信金额
        creditApplyDto.setCreditAmt(credit.getCreditAmt());
        // 借款金额
        creditApplyDto.setLoanAmt(credit.getCreditAmt());
        creditApplyDto.setLoanPurpose(EnumConvert.INSTANCE.toCoreApi(credit.getLoanPurpose()));
        creditApplyDto.setPeriods(credit.getPeriods());
        creditApplyDto.setCustomRate(QhBank.getQhBankBy(credit.getBankChannel()).getBankCustomRate());

        //额度合同编号
        creditApplyDto.setCreditApplyContractNo(order.getId());

        // 包装用户信息
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setLivingStreet(creditUserInfo.getLivingStreet());
        userInfoDto.setUnitStreet(creditUserInfo.getUnitStreet());
        userInfoDto.setIndustry(Optional.ofNullable(creditUserInfo.getIndustry()).orElse(Industry.TWENTY).name());
        userInfoDto.setPosition(Optional.ofNullable(creditUserInfo.getPosition()).orElse(Position.ELEVEN).name());
        userInfoDto.setFaceChannel(creditUserInfo.getFaceChannel());
        userInfoDto.setFaceTime(creditUserInfo.getFaceTime().format(DateTimeFormatter.BASIC_ISO_DATE));
        userInfoDto.setFaceScore(creditUserInfo.getFaceScore());
        // 经纬度
        String gps = creditUserInfo.getDeviceGps();
        userInfoDto.setLatitude(toLatitude(gps));
        userInfoDto.setLongitude(toLongitude(gps));
        userInfoDto.setMac(creditUserInfo.getDeviceMac());
        userInfoDto.setName(creditUserInfo.getName());
        userInfoDto.setAcardScore(creditUserInfo.getAcardScore());
        userInfoDto.setBcardScore(creditUserInfo.getBcardScore());
        userInfoDto.setMarriage(EnumConvert.INSTANCE.toCoreApi(creditUserInfo.getMarriage()));
        userInfoDto.setEducation(EnumConvert.INSTANCE.toCoreApi(creditUserInfo.getEducation()));
        userInfoDto.setMobile(creditUserInfo.getMobile());
        userInfoDto.setLivingAddress(creditUserInfo.getLivingAddress());
        userInfoDto.setLivingProvinceCode(creditUserInfo.getLivingProvinceCode());
        userInfoDto.setLivingCityCode(creditUserInfo.getLivingCityCode());
        userInfoDto.setLivingDistrictCode(creditUserInfo.getLivingDistrictCode());
        userInfoDto.setUnit(creditUserInfo.getUnit());
        userInfoDto.setUnitAddress(creditUserInfo.getUnitAddress());
        userInfoDto.setUnitProvinceCode(creditUserInfo.getUnitProvinceCode());
        userInfoDto.setUnitCityCode(creditUserInfo.getUnitCityCode());
        userInfoDto.setUnitDistrictCode(creditUserInfo.getUnitDistrictCode());
        //userInfoDto.setIncome(creditUserInfo.getIncome().toString());
        userInfoDto.setIncome(creditUserInfo.getIncome() == null ? null : creditUserInfo.getIncome().toString());
        creditApplyDto.setUserInfo(userInfoDto);

        // 身份证相关信息
        IdCardInfoDto idCardInfoDto = new IdCardInfoDto();
        idCardInfoDto.setName(creditUserInfo.getName());
        idCardInfoDto.setNation(creditUserInfo.getCertNation());
        idCardInfoDto.setCertAddress(creditUserInfo.getCertAddress());
        idCardInfoDto.setCertNo(creditUserInfo.getCertNo());
        idCardInfoDto.setProvinceCode(creditUserInfo.getCertProvinceCode());
        idCardInfoDto.setCityCode(creditUserInfo.getCertCityCode());
        idCardInfoDto.setDistrictCode(creditUserInfo.getCertDistrictCode());
        idCardInfoDto.setCertSignOrg(creditUserInfo.getCertSignOrg());
        idCardInfoDto.setCertValidStart(creditUserInfo.getCertValidStart());
        idCardInfoDto.setCertValidEnd(creditUserInfo.getCertValidEnd());
        creditApplyDto.setIdCardInfo(idCardInfoDto);
        // 银行卡信息
        if (StringUtil.isNotBlank(creditUserInfo.getCardNo())) {
            BankCardInfoDto bankCardInfoDto = new BankCardInfoDto();
            bankCardInfoDto.setPayChannel(CardConvert.INSTANCE.toCoreProtocolChannel(creditUserInfo.getCardChannel()));
            bankCardInfoDto.setProtocolNo(creditUserInfo.getCardAgreeNo());
            bankCardInfoDto.setProtocolUserNo(creditUserInfo.getCardMerchantNo());
            bankCardInfoDto.setCardName(creditUserInfo.getCardName());
            bankCardInfoDto.setPhone(creditUserInfo.getCardPhone());
            bankCardInfoDto.setCertNo(creditUserInfo.getCertNo());
            bankCardInfoDto.setBankCode(creditUserInfo.getCardBankCode());
            bankCardInfoDto.setCardNo(creditUserInfo.getCardNo());
            bankCardInfoDto.setBankName(creditUserInfo.getCardBankName());
            creditApplyDto.setBankCardInfo(bankCardInfoDto);
        }

        // 协议

        List<FileInfoDto> fileInfoDtoList = new ArrayList<>();
        for (UserFile creditFile : creditFiles) {
            FileInfoDto fileInfoDto = new FileInfoDto();
            FileType fileType = EnumConvert.INSTANCE.toCoreApi(creditFile.getFileType());
            if (Objects.isNull(fileType)) {
                continue;
            }
            fileInfoDto.setFileType(fileType);
            fileInfoDto.setOssBucket(creditFile.getOssBucket());
            fileInfoDto.setOssKey(creditFile.getOssKey());
            fileInfoDtoList.add(fileInfoDto);
        }
        creditApplyDto.setFileInfoDtoList(fileInfoDtoList);

        // 联系人
        List<CreditUserContactInfo> creditUserContactInfos = creditUserContactInfoRepository.findByCreditUserId(creditUserInfo.getId());
        List<UserContactInfoDto> userContactInfoDtoList = new ArrayList<>();
        for (CreditUserContactInfo creditUserContactInfo : creditUserContactInfos) {
            UserContactInfoDto userContactInfoDto = new UserContactInfoDto();
            userContactInfoDto.setPhone(creditUserContactInfo.getPhone());
            userContactInfoDto.setName(creditUserContactInfo.getName());
            userContactInfoDto.setRelation(getRelation(creditUserContactInfo.getRelation().name()));
            userContactInfoDtoList.add(userContactInfoDto);
        }
        creditApplyDto.setUserContactInfoDtoList(userContactInfoDtoList);
        //目前是长银需求 后续扩展需新增
        extendedFieldAdjustment(credit, order, creditApplyDto);
        logger.info("授信参数组装:{}", JsonUtil.toJsonString(creditApplyDto));
        return creditApplyDto;
    }

    private void extendedFieldAdjustment(Credit credit, Order order, CreditApplyDto<ExtInfoDto> creditApplyDto) {
        Optional.ofNullable(order.getApplyChannel()).ifPresent(applyChannel -> {
            if (BankChannel.CYBK.equals(credit.getBankChannel())) {
                CybkExtInfoDto cybkExtInfoDto = new CybkExtInfoDto();
                cybkExtInfoDto.setApplyChannel(applyChannel);
                ExtInfoDto extInfo = new ExtInfoDto();
                extInfo.setCybkExtInfo(cybkExtInfoDto);
                creditApplyDto.setExtInfo(extInfo);
            }
        });
    }

    private Relation getRelation(String name) {
        for (Relation item : Relation.values()) {
            if (Objects.equals(item.name(), name)) {
                return item;
            }
        }
        return Relation.UNKNOWN;
    }

    public void bankCreditResult(String creditId) {
        logger.info("查询core授信结果:{}", creditId);
        Credit credit = creditRepository.findById(creditId).orElseThrow();
        //
        CreditQueryDto creditQueryDto = new CreditQueryDto();
        creditQueryDto.setCreditId(credit.getCreditNo());
        creditQueryDto.setSysId(creditId);
        RestResult<CreditResultDto> restResult = finCreditService.queryResult(creditQueryDto);
        logger.info("查询core授信结果返回:{}", JsonUtil.toJsonString(restResult));
        if (ResultCode.SYS_ERROR.getCode().equals(restResult.getCode())) {
            throw new BizException(restResult.getMsg(), ResultCode.CREDIT_ERROR);
        }
        CreditResultDto creditResult = new CreditResultDto();
        if (ResultCode.BIZ_ERROR.getCode().equals(restResult.getCode()) && "授信不存在".equals(restResult.getMsg())) {
            creditResult.setStatus(ProcessStatus.FAIL);
        } else {
            creditResult = restResult.getData();
        }

        ProcessStatus creditStatus = creditResult.getStatus();
        if (!ProcessStatus.FAIL.equals(creditStatus) && !ProcessStatus.SUCCESS.equals(creditStatus)) {
            mqService.submitCreditResultQueryDelay(creditId);
            return;
        }
        //亲家-亿联 授信金额不能小于申请金额
        //todo 分期乐mock暂时跳过授信金额校验，与资方联调时需删除
        if(FlowChannel.FQLQY001.name().equals(credit.getFlowChannel().name())){
            if (ProcessStatus.FAIL.equals(creditStatus)
                && (credit.getBankChannel() == BankChannel.CYBK)) {
                creditStatus = ProcessStatus.FAIL;
                credit.setFailReason(ResultCode.CREDIT_AMOUNT_ERROR.getMsg());
            }
        }else {
            BigDecimal creditAmt = credit.getCreditAmt();
            if (ProcessStatus.SUCCESS.equals(creditStatus)
                && (credit.getBankChannel() == BankChannel.CYBK)
                && creditResult.getCreditResultAmt().compareTo(creditAmt) < 0) {
                creditStatus = ProcessStatus.FAIL;
                credit.setFailReason(ResultCode.CREDIT_AMOUNT_ERROR.getMsg());
            }
        }
        // credit
        credit.setState(EnumConvert.INSTANCE.toCashState(creditStatus));
        credit.setFailReason(StringUtil.isNotBlank(credit.getFailReason()) ? credit.getFailReason() : creditResult.getFailMsg());
        credit.setCreditNo(creditResult.getCreditId());
        credit.setPassTime(creditResult.getCreditTime());
        credit.setCapExpireTime(creditResult.getCapExpireTime());
        creditRepository.save(credit);

        // 回到路由,处理后续处理后续
        mqService.submitCreditRouteResult(creditId);

    }

    public void bankRecredit(String creditId) {
        Credit credit = creditRepository.findById(creditId).orElseThrow();

        // 去core重新授信
        logger.info("申请core重新授信:{}", creditId);
        RecreditApplyDto recreditApplyDto = new RecreditApplyDto();
        recreditApplyDto.setSysId(creditId);
        recreditApplyDto.setBankChannel(credit.getBankChannel());
        recreditApplyDto.setFlowChannel(EnumConvert.INSTANCE.toCoreApi(credit.getFlowChannel()));
        RestResult<CreditResultDto> restResult = finCreditService.recredit(recreditApplyDto);

        logger.error("申请core重新授信结果:{}", JsonUtil.toJsonString(restResult));
        if (!restResult.isSuccess()) {
            // 同步失败原因
            credit.setFailReason(restResult.getMsg());
            creditRepository.save(credit);
            throw new BizException(restResult.getMsg(), ResultCode.BIZ_ERROR);
        }
        // 保存core授信编号
        CreditResultDto creditResultDto = restResult.getData();
        credit.setCreditNo(creditResultDto.getCreditId());
        credit.setState(ProcessState.PROCESSING);
        creditRepository.save(credit);

        mqService.submitRecreditResultQueryDelay(creditId);
    }

    public void bankRecreditResult(String creditId) {
        logger.info("查询core重新授信结果:{}", creditId);
        Credit credit = creditRepository.findById(creditId).orElseThrow();

        CreditQueryDto creditQueryDto = new CreditQueryDto();
        creditQueryDto.setCreditId(credit.getCreditNo());
        creditQueryDto.setSysId(creditId);
        RestResult<CreditResultDto> restResult = finCreditService.queryResult(creditQueryDto);
        logger.info("查询core授信结果返回:{}", JsonUtil.toJsonString(restResult));
        if (ResultCode.SYS_ERROR.getCode().equals(restResult.getCode())) {
            throw new BizException(restResult.getMsg(), ResultCode.CREDIT_ERROR);
        }
        CreditResultDto creditResult = new CreditResultDto();
        if (ResultCode.BIZ_ERROR.getCode().equals(restResult.getCode()) && "授信不存在".equals(restResult.getMsg())) {
            creditResult.setStatus(ProcessStatus.FAIL);
        } else {
            creditResult = restResult.getData();
        }

        ProcessStatus creditStatus = creditResult.getStatus();
        if (!ProcessStatus.FAIL.equals(creditStatus) && !ProcessStatus.SUCCESS.equals(creditStatus)) {
            mqService.submitRecreditResultQueryDelay(creditId);
            return;
        }

        if (ProcessStatus.SUCCESS == creditStatus
                && (credit.getBankChannel() == BankChannel.CYBK
                && creditResult.getCreditResultAmt().compareTo(credit.getCreditAmt()) < 0)) {

            creditStatus = ProcessStatus.FAIL;
            credit.setFailReason(ResultCode.CREDIT_AMOUNT_ERROR.getMsg());
        }

        // credit
        credit.setState(EnumConvert.INSTANCE.toCashState(creditStatus));
        credit.setFailReason(StringUtil.isNotBlank(credit.getFailReason()) ? credit.getFailReason() : creditResult.getFailMsg());
        credit.setCreditNo(creditResult.getCreditId());
        credit.setPassTime(creditResult.getCreditTime());
        credit.setCapExpireTime(creditResult.getCapExpireTime());
        creditRepository.save(credit);

        // loan
        Loan loan = loanRepository.findByCreditId(creditId);

        if (ProcessState.SUCCEED == credit.getState()) {
            //重新授信成功激活挂起订单
            loanCommonService.suspendActive(loan.getId());
        }

        if (ProcessState.FAILED == credit.getState()) {
            //重新授信失败更新order表为放款失败
            Order order = orderRepository.findOrderById(credit.getOrderId());
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark(credit.getFailReason());
            orderRepository.save(order);

            //更新loan表为放款失败
            loan.setLoanState(ProcessState.FAILED);
            loan.setFailReason(order.getRemark());
            loanRepository.save(loan);

            eventPublisher.publishEvent(new LoanResultEvent(loan.getId(), loan.getLoanState(),
                    loan.getLoanTime(), loan.getBankChannel(), loan.getFailReason()));
        }
    }

    static String toGps(String gps) {
        if (StringUtil.isBlank(gps)) {
            return "";
        }
        String[] split = gps.split(",", -1);
        if ("null".equals(split[0])) {
            return "";
        }
        if ("null".equals(split[1])) {
            return "";
        }
        return gps;
    }

    static String toLatitude(String gps) {
        gps = toGps(gps);
        if (StringUtil.isBlank(gps)) {
            return gps;
        }
        return gps.split(",", -1)[0];
    }

    static String toLongitude(String gps) {
        gps = toGps(gps);
        if (StringUtil.isBlank(gps)) {
            return gps;
        }
        if (gps.split(",", -1).length < 2) {
            return "";
        }
        return gps.split(",", -1)[1];
    }


}
