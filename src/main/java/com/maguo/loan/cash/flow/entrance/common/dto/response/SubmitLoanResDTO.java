package com.maguo.loan.cash.flow.entrance.common.dto.response;

import java.math.BigDecimal;

/**
 * 提交要款返回结构
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
public class SubmitLoanResDTO {

    /**
     * 放款金额
     */
    private BigDecimal amount;
    /**
     * 放款期数
     */
    private Integer period;
    /**
     * 订单状态
     */
    private String orderState;
    /**
     * 合作机构单号
     */
    private String partnerOrderNo;
    /**
     * 我方单号
     */
    private String orderId;

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }
}
