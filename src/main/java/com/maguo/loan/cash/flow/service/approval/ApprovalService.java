package com.maguo.loan.cash.flow.service.approval;


import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.Duration;

/**
 * 审批服务
 *
 * <AUTHOR>
 */
@Service
public class ApprovalService {

    private static final Logger logger = LoggerFactory.getLogger(ApprovalService.class);

    private static final Duration APPROVAL_LOCK_WAIT = Duration.ofSeconds(60);

    @Autowired
    private LockService lockService;

    @Autowired
    private MqService mqService;

    @Autowired
    private PreOrderRepository preOrderRepository;


    /**
     * 异步进件
     *
     * @param preOrderId 预订单id
     */
    public void approvalApply(String preOrderId) {
        PreOrder preOrder = preOrderRepository.findById(preOrderId).orElseThrow();
        if (PreOrderState.INIT != preOrder.getPreOrderState()) {
            return;
        }
        Locker lock = lockService.getLock(RedisKeyConstants.APPROVAL_APPLY + preOrder.getCertNo());
        try {
            boolean locked = lock.tryLock(Duration.ZERO, APPROVAL_LOCK_WAIT);
            if (!locked) {
                //未获取到锁，延迟处理
              //  mqService.submitApprovalApplyDelay(preOrder, null);
                return;
            }

            StopWatch sw = new StopWatch();
            sw.start();
            //获取对应进件服务类处理
            //getApprovalService(preOrder.getFlowChannel()).commonApproval(preOrder);
            sw.stop();
            logger.info("进件: flowChannel = {}, preOrderId = [{}], 耗时 = {}ms ", preOrder.getFlowChannel(), preOrderId, sw.getTotalTimeMillis());
        } catch (InterruptedException e) {
            //ignore
        } finally {
            lock.unlock();
        }

    }


}
