package com.maguo.loan.cash.flow.service.event.listener;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserRenewedRecord;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.MarkStatusFlow;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.SceneEnum;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserRenewedRecordRepository;
import com.maguo.loan.cash.flow.repository.UserRenewedTagRepository;
import com.maguo.loan.cash.flow.service.CheckService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.SmsService;
import com.maguo.loan.cash.flow.service.event.RepaySucceedResultEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 还款成功事件
 *
 * <AUTHOR>
 */
@Component
public class RepaySucceedResultEventListener {

    private static final Logger logger = LoggerFactory.getLogger(RepaySucceedResultEventListener.class);

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private MqService mqService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private SmsService smsService;

    @Autowired
    private UserInfoRepository userInfoRepository;


    @Autowired
    private UserRenewedRecordRepository userRenewedRecordRepository;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private CheckService checkService;

    @Autowired
    private UserRenewedTagRepository userRenewedTagRepository;

    @EventListener(RepaySucceedResultEvent.class)
    public void onApplicationEvent(RepaySucceedResultEvent repaySucceedResultEvent) {
        CustomRepayRecord record = customRepayRecordRepository.findById(repaySucceedResultEvent.getCustomRecordId()).orElseThrow();

        Loan loan = loanRepository.findById(record.getLoanId()).orElseThrow();
        repayCallback(loan, record);
        UserInfo userInfo = userInfoRepository.findById(loan.getUserId()).orElseThrow();
        //handlerRenewedFlag(record, loan, userInfo);
    }

    /**
     * 初始化续借标识
     *
     * @param record   record
     * @param loan     loan
     * @param userInfo userInfo
     */
    private void handlerRenewedFlag(CustomRepayRecord record, Loan loan, UserInfo userInfo) {
        try {
            if (!checkService.needQueryRenewedFlag(userInfo.getId())) {
                logger.info("用户:{}不需要查询续借标识", userInfo.getId());
                userRenewedTagRepository.findById(userInfo.getId()).ifPresent(userRenewedTag -> {
                    if (userRenewedTag.getRenewedFlag() == WhetherState.Y) {
                        userRenewedTag.setRenewedFlag(WhetherState.N);
                        userRenewedTagRepository.save(userRenewedTag);
                    }
                });
                return;
            }
            UserRenewedRecord userRenewed = userRenewedRecordRepository.findTopBySourceIdOrderByCreatedTimeDesc(record.getId());
            if (userRenewed == null) {
                userRenewed = new UserRenewedRecord();
                // 还款计划
                RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(record.getLoanId(), record.getPeriod());
                // 请求ID
                userRenewed.setUserId(userInfo.getId());
                userRenewed.setName(userInfo.getName());
                userRenewed.setMobile(userInfo.getMobile());
                userRenewed.setCertNo(userInfo.getCertNo());
                userRenewed.setRenewedFlag(WhetherState.N);
                userRenewed.setRepayPlanId(repayPlan.getLoanId());
                userRenewed.setCustomRepayRecordId(record.getId());
                userRenewed.setExpireTime(LocalDate.now());
                userRenewed.setOrderId(loan.getOrderId());
                userRenewed.setStatus(MarkStatusFlow.INIT);
                userRenewed.setScene(SceneEnum.REPAYMENT);
                userRenewed.setSourceId(record.getId());
                userRenewed = userRenewedRecordRepository.save(userRenewed);
            }
            //续借标识请求风控
            mqService.submitRenewedQuery(userRenewed.getId());
        } catch (Exception e) {
            logger.error("初始化续借标识失败,userId:{}", loan.getUserId(), e);
        }
    }


    public void repayCallback(Loan loan, CustomRepayRecord customRepayRecord) {


        if (customRepayRecord.getRepayPurpose() == RepayPurpose.CLEAR || Objects.equals(customRepayRecord.getPeriod(), loan.getPeriods())) {
            //订单更新为结清
            String orderId = loan.getOrderId();
            Order order = orderRepository.findById(orderId).orElseThrow();
            order.setOrderState(OrderState.CLEAR);
            orderRepository.save(order);

            //结清回调
            CallBackDTO clearCallBackDTO = new CallBackDTO();
            clearCallBackDTO.setFlowChannel(loan.getFlowChannel());
            clearCallBackDTO.setCallbackState(CallbackState.CLEAR);
            clearCallBackDTO.setBusinessId(orderId);
            mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(clearCallBackDTO));

        }
        //还款回调
        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setFlowChannel(loan.getFlowChannel());
        callBackDTO.setCallbackState(CallbackState.REPAID);
        callBackDTO.setBusinessId(customRepayRecord.getId());
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
    }

}
