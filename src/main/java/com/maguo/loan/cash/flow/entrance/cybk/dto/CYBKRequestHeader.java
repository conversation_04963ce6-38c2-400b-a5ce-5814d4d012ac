package com.maguo.loan.cash.flow.entrance.cybk.dto;

import java.io.Serializable;

public class CYBKRequestHeader implements Serializable {
    private static final long serialVersionUID = -8614861466935614104L;
    private String version = "01";
    private String channel;
    private String appId;
    private String reqNo;
    private String reqDateTime;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getReqDateTime() {
        return reqDateTime;
    }

    public void setReqDateTime(String reqDateTime) {
        this.reqDateTime = reqDateTime;
    }

    public String getChannel() {
        return this.channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getAppId() {
        return this.appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getReqNo() {
        return this.reqNo;
    }

    public void setReqNo(String reqNo) {
        this.reqNo = reqNo;
    }

}
