package com.maguo.loan.cash.flow.entrance.cybk.controller;

import com.jinghang.capital.api.LoanService;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonRequest;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonResponse;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cybk/api/loan/v1")
public class CYBKLoanController {

    @Autowired
    private LoanService loanService;

    private static Logger logger = LoggerFactory.getLogger(CYBKLoanController.class);


    /**
     * 放款结果回调
     * @param cybkCommonRequest
     * @return
     */
    @PostMapping("/loanResultBack")
    public CYBKCommonResponse upQuotaBack(@RequestBody CYBKCommonRequest cybkCommonRequest) {
        logger.info("放款结果回调接收参数：{}", JsonUtil.toJsonString(cybkCommonRequest));
        String bodyString = cybkCommonRequest.getBody().toString();
        //组装公共回调报文
        BankResultBackDto bankResultBackDto = new BankResultBackDto();
        bankResultBackDto.setBankChannel(BankChannel.CYBK);
        bankResultBackDto.setJson(bodyString);
        //回调资金系统
        bankResultBackDto = loanService.loanResultBack(bankResultBackDto).getData();
        //组装长银响应报文
        CYBKCommonResponse cybkCommonResponse = CommonResult.assembleResponse(cybkCommonRequest, bankResultBackDto.getJson());
        logger.info("放款结果回调响应参数：{}", JsonUtil.toJsonString(cybkCommonResponse));
        return cybkCommonResponse;
    }
}
