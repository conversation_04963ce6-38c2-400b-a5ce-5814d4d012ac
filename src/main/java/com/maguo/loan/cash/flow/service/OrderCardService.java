package com.maguo.loan.cash.flow.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.FlowConfig;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.OrderBindCardRecord;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.repository.CapitalConfigRepository;
import com.maguo.loan.cash.flow.repository.FlowConfigRepository;
import com.maguo.loan.cash.flow.repository.OrderBindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class OrderCardService {

    @Autowired
    private OrderBindCardRecordRepository orderBindCardRecordRepository;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private CapitalConfigRepository capitalConfigRepository;

    @Autowired
    private FlowConfigRepository flowConfigRepository;

    public void genOrderCardRecord(Order order) {
        //绑卡渠道
        FlowConfig flowConfig = flowConfigRepository.findByFlowChannel(order.getFlowChannel()).orElseThrow();
        OrderBindCardRecord orderBindCardRecord = new OrderBindCardRecord();
        orderBindCardRecord.setId(order.getId());
        orderBindCardRecord.setUserId(order.getUserId());
        orderBindCardRecord.setFlowChannel(order.getFlowChannel());
        orderBindCardRecord.setFirstProtocolChannel(flowConfig.getFirstProtocolChannel());
        orderBindCardRecord.setSecondProtocolChannel(flowConfig.getSecondProtocolChannel());
        orderBindCardRecordRepository.save(orderBindCardRecord);
    }

    public OrderBindCardRecord getOrderBindCard(String orderId) {
        return orderBindCardRecordRepository.findById(orderId).orElse(null);
    }

    public UserBankCard getCreditCard(Order order, BankChannel bankChannel) {
        String userBankCardId;

//        OrderBindCardRecord cardRecord = orderBindCardRecordRepository.findById(order.getId()).orElse(null);
//        if (Objects.isNull(cardRecord)) {
//            userBankCardId = order.getLoanCardId();
//        } else {
//            CapitalConfig capitalConfig = capitalConfigRepository.findByBankChannel(bankChannel).orElseThrow();
//
//            if (cardRecord.getFirstProtocolChannel() == capitalConfig.getProtocolChannel()) {
//                userBankCardId = cardRecord.getFirstCardId();
//            } else {
//                userBankCardId = cardRecord.getSecondCardId();
//            }
//
//            //纯api不会记录订单绑卡记录
//            if (Objects.isNull(userBankCardId)) {
//                userBankCardId = order.getLoanCardId();
//            }
//
//        }
        userBankCardId = order.getLoanCardId();
        return userBankCardRepository.findById(userBankCardId).orElseThrow(null);
    }
}
