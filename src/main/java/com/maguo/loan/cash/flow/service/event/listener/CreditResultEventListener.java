package com.maguo.loan.cash.flow.service.event.listener;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.event.CreditResultEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 授信事件
 *
 * <AUTHOR>
 * @date 2023/10/14
 */
@Component
public class CreditResultEventListener {

    private static final Logger logger = LoggerFactory.getLogger(CreditResultEventListener.class);

    @Autowired
    private MqService mqService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private LoanService loanService;

    @Autowired
    private WarningService warningService;




    @EventListener(CreditResultEvent.class)
    public void onApplicationEvent(CreditResultEvent event) {
        Order order = orderRepository.findById(event.getOrderId()).orElseThrow();
        CallbackState callbackState = null;
        switch (order.getOrderState()) {
            case CREDIT_FAIL -> {
                callbackState = CallbackState.CREDIT_FAIL;
            }
            case CREDIT_PASS, LOANING -> {
                callbackState = CallbackState.CREDIT_PASS;
                try {
                    //修改订单状态为LOANING
                    order.setOrderState(OrderState.LOANING);
                    order = orderRepository.save(order);
                    loanService.apply(event.getCreditId());
                } catch (Exception e) {
                    warningService.warn("授信通过直接放款异常:" + e.getMessage(), logger::error);
                }
            }
            default -> {
            }
        }
        //回调
        //notifyFlow(order, callbackState);
    }


    /**
     * 通知流量
     *
     * @param order
     * @param callbackState
     */
    private void notifyFlow(Order order, CallbackState callbackState) {
        //授信结果通知
        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setFlowChannel(order.getFlowChannel());
        callBackDTO.setBusinessId(order.getId());
        callBackDTO.setCallbackState(callbackState);
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
    }

}
