package com.maguo.loan.cash.flow.entrance.cybk.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CYBKConfig {


    @Value("${cybk.appid}")
    private String appid;
    @Value("${cybk.channel}")
    private String channel;

    /**
     * 业务数据 加密密钥
     */
    @Value("${cybk.content.key}")
    private String contentKey;

    /**
     * MD5后的签名 加密密钥
     */
    @Value("${cybk.sign.key}")
    private String signKey;
    /**
     * 是否跳过验签 默认为false
     */
    @Value("${cybk.skipSignVerify}")
    private boolean skipSignVerify;

    public String getSignKey() {
        return signKey;
    }

    public String getChannel() {
        return channel;
    }

    public String getContentKey() {
        return contentKey;
    }

    public String getAppid() {
        return appid;
    }

    public boolean isSkipSignVerify() {
        return skipSignVerify;
    }
}
