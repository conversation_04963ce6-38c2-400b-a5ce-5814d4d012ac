package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entrance.fql.service.FenQiLeService;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class FqlApprovalApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(FqlApprovalApplyListener.class);

    public FqlApprovalApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private FenQiLeService fenQiLeService;

    @RabbitListener(queues = RabbitConfig.Queues.FQL_CREDIT_USER_FILE_DOWNLOAD)
    public void listenCreditApply(Message message, Channel channel) {
        String applyRecordId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听分期乐影像件异步下载:{}", applyRecordId);
            fenQiLeService.FqlUserFileDownloadAndUpload(applyRecordId);
        } catch (Exception e) {
            processException(applyRecordId, message, e, "分期乐影像件异步下载异常", getMqService()::submitCreditUserFileDownloadDelay);
        } finally {
            ackMsg(applyRecordId, message, channel);
        }
    }
}
