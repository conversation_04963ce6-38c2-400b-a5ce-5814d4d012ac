package com.maguo.loan.cash.flow.service.common.agreement;


import com.maguo.loan.cash.flow.dto.agreement.AgreementDesc;
import com.maguo.loan.cash.flow.dto.agreement.AgreementItem;
import com.maguo.loan.cash.flow.dto.agreement.AgreementQueryRequest;
import com.maguo.loan.cash.flow.entity.AgreementTemplateApp;
import com.maguo.loan.cash.flow.repository.AgreementTemplateAppRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-11-18
 */
@Service
public class AgreementAppService {

    @Autowired
    private AgreementTemplateAppRepository agreementTemplateAppRepository;

    /**
     * 查询协议
     * @param request request
     * @return List<AgreementDesc>
     */
    public List<AgreementDesc> query(AgreementQueryRequest request) {
        List<String> list = Arrays.stream(request.getScene().split(",")).toList();
        List<AgreementTemplateApp> scene = agreementTemplateAppRepository.findBySceneIn(list);
        //具体协议条目
        List<AgreementItem> agreementItem = new ArrayList<>();
        HashSet<String> set = new HashSet<>();
        //遍历当前所有协议
        for (AgreementTemplateApp agreementTemplateApp : scene) {
            if (Objects.equals(agreementTemplateApp.getIsSplicing(), request.getIsSplicing() == null ? "Y" : request.getIsSplicing())) {
                AgreementItem item = new AgreementItem();
                //协议具体目录名字
                item.setName(agreementTemplateApp.getName());
                //协议url
                item.setUrl(agreementTemplateApp.getFileUrl());
                agreementItem.add(item);
                set.add(agreementTemplateApp.getCatalogName());
            }
        }
        AgreementDesc desc = new AgreementDesc();
        desc.setScene(request.getScene());
        desc.setName(StringUtils.strip(set.toString(), "[]").replace(",", ""));
        desc.setItems(agreementItem);
        desc.setNums(agreementItem.size());
        List<AgreementDesc> agreementDesc = new ArrayList<>();
        agreementDesc.add(desc);
        return agreementDesc;
    }
}
