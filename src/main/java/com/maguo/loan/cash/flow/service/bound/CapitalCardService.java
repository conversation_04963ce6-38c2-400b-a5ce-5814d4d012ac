package com.maguo.loan.cash.flow.service.bound;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.BindSignMode;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.SignStatus;
import com.jinghang.capital.api.dto.credit.BindApplyDto;
import com.jinghang.capital.api.dto.credit.BindConfirmDto;
import com.jinghang.capital.api.dto.credit.BindResultDto;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.CardConvert;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.BindCardRelation;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.CreditUserInfo;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.remote.cardbin.impl.AlipayCardBinService;
import com.maguo.loan.cash.flow.remote.cardbin.impl.LocalCardBinService;
import com.maguo.loan.cash.flow.remote.core.FinBindService;
import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestService;
import com.maguo.loan.cash.flow.repository.BindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.BindCardRelationRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.CreditUserInfoRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import com.maguo.loan.cash.flow.vo.bound.BindResultVo;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/10/14
 */
@Service
public class CapitalCardService {
    private static final Logger logger = LoggerFactory.getLogger(CapitalCardService.class);
    private final LoanRepository loanRepository;
    private BindCardRecordRepository recordRepository;
    private BindCardRelationRepository relationRepository;
    private UserBankCardRepository userBankCardRepository;
    private FinBindService finBindService;
    private CreditUserInfoRepository creditUserInfoRepository;
    private CreditRepository creditRepository;
    private CommonRequestService commonRequestService;
    @Resource
    private UserInfoRepository userInfoRepository;
    @Autowired
    private LocalCardBinService localCardBinService;

    @Autowired
    private AlipayCardBinService alipayCardBinService;


    /**
     * 不需要资方绑卡，由平台代绑卡
     */
    private static final List<BankChannel> NO_BIND_CHANNELS = List.of(
    BankChannel.HXBK
    );


    public CapitalCardService(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    /**
     * 提现绑卡
     *
     * @param credit
     * @return
     */
    public BindCardRecord bindApply(Credit credit) {
        if (NO_BIND_CHANNELS.contains(credit.getBankChannel())) {
            // 不再代绑卡，默认返回绑定成功
            return bindApplySubstitute(credit);
        }
        // 默认去资方绑卡
        return bindApplyNormal(credit);
    }

    /**
     * 提现绑卡确认
     *
     * @param recordId
     * @param smsCode
     * @return
     */
    public BindResultVo bindConfirm(String recordId, String smsCode) {
        BindCardRecord bindCardRecord = recordRepository.findById(recordId).orElseThrow(() -> new BizException(ResultCode.CARD_RECORD_NOT_EXIST));

        if (ProcessState.FAILED == bindCardRecord.getState()) {
            BindResultVo bindResultVo = new BindResultVo();
            bindResultVo.setState(ProcessState.FAILED);
            bindResultVo.setFailReason("绑卡确认失败,请重新获取验证码");
            return bindResultVo;
        }

        BindCardRelation bindCardRelation = relationRepository.findByBindCardApplyId(bindCardRecord.getId())
            .orElseThrow(() -> new BizException(ResultCode.CARD_RECORD_RELATION_NOT_EXIST));
        Credit credit = creditRepository.findById(bindCardRelation.getRelatedId()).orElseThrow(() -> new BizException(ResultCode.CREDIT_NOT_FOUND));

        if (NO_BIND_CHANNELS.contains(credit.getBankChannel())) {
            return bindConfirmSubstitute(bindCardRecord, credit);
        }

        return bindConfirmNormal(bindCardRecord, credit, smsCode);
    }

    /**
     * 申请fin-core绑卡
     *
     * @param credit
     * @return
     */
    private BindCardRecord bindApplyNormal(Credit credit) {
        CreditUserInfo creditUserInfo = creditUserInfoRepository.findByCreditId(credit.getId())
            .orElseThrow(() -> new BizException(ResultCode.USER_INFO_NOT_EXIST));
        //
        BindCardRecord bindCardRecord = initRecord(credit, creditUserInfo);
        //
        BindApplyDto bindApplyDto = new BindApplyDto();
        bindApplyDto.setBankChannel(credit.getBankChannel());
        bindApplyDto.setSysCreditId(credit.getId());
        bindApplyDto.setCardName(creditUserInfo.getCardName());
        bindApplyDto.setCardNo(creditUserInfo.getCardNo());
        bindApplyDto.setCertNo(creditUserInfo.getCertNo());
        bindApplyDto.setPhone(creditUserInfo.getCardPhone());
        bindApplyDto.setBankCode(creditUserInfo.getCardBankCode());
        bindApplyDto.setBankName(creditUserInfo.getCardBankName());
        bindApplyDto.setAgreementNo(creditUserInfo.getCardAgreeNo());
        bindApplyDto.setBindSignMode(BindSignMode.SHARE);
        bindApplyDto.setProtocolChannel(CardConvert.INSTANCE.toCoreProtocolChannel(creditUserInfo.getCardChannel()));
        // 申请资方绑卡
        logger.info("申请core绑卡,cardId:{}, 入参: {}", bindCardRecord.getId(), JsonUtil.toJsonString(bindApplyDto));
        RestResult<BindResultDto> bindRestResult = finBindService.bindApply(bindApplyDto);
        logger.info("core绑卡结果: {}", JsonUtil.toJsonString(bindRestResult));
        BindResultDto bindResultDto = bindRestResult.getData();
        if (!bindRestResult.isSuccess()) {
            String msg = !bindRestResult.isSuccess() ? bindRestResult.getMsg() : bindResultDto.getMsg();

            bindCardRecord.setFailReason(msg);
            bindCardRecord.setState(ProcessState.FAILED);
            recordRepository.save(bindCardRecord);

            return bindCardRecord;
        } else if (ProcessStatus.FAIL == bindResultDto.getStatus()) {
            bindCardRecord.setFailReason(bindResultDto.getMsg());
            bindCardRecord.setState(ProcessState.FAILED);
            recordRepository.save(bindCardRecord);
            return bindCardRecord;
        }

        bindCardRecord.setConfirmOrderNo(bindResultDto.getSysId());

        if (SignStatus.Y == bindResultDto.getSignStatus()) {
            // 已签约
            bindCardRecord.setAgreeNo(bindResultDto.getAgreementNo());
            bindCardRecord.setState(ProcessState.SUCCEED);
            UserBankCard userBankCard = createUserBankCard(bindCardRecord);
            userBankCardRepository.save(userBankCard);
        } else {
            bindCardRecord.setState(ProcessState.PROCESSING);
        }
        return recordRepository.save(bindCardRecord);

    }

    private BindResultVo bindConfirmNormal(BindCardRecord bindCardRecord, Credit credit, String smsCode) {
        // 参数
        BindConfirmDto bindConfirmDto = new BindConfirmDto();
        bindConfirmDto.setBankChannel(credit.getBankChannel());
        bindConfirmDto.setBandCardId(bindCardRecord.getConfirmOrderNo());
        bindConfirmDto.setSms(smsCode);
        bindConfirmDto.setSysCreditId(credit.getId());
        // 请求
        logger.info("申请core绑卡,cardId:{} ,入参:{}", bindCardRecord.getId(), JsonUtil.toJsonString(bindConfirmDto));
        RestResult<BindResultDto> bindRestResult = finBindService.bindConfirm(bindConfirmDto);
        logger.info("申请core绑卡确认结果:{}", JsonUtil.toJsonString(bindRestResult));
        if (!bindRestResult.isSuccess()) {
            throw new BizException(ResultCode.CARD_CONFIRM_FAIL);
        }

        bindCardRecord = getBindCardRecord(bindCardRecord, bindRestResult, bindConfirmDto.getBankChannel());
        //
        BindResultVo bindResultVo = CardConvert.INSTANCE.toVo(bindCardRecord);
        bindResultVo.setCreditId(credit.getId());
        bindResultVo.setOrderId(credit.getOrderId());
        return bindResultVo;
    }

    /**
     * 目前亲家临商&亲家亿联不需要代替资方绑卡，所以直接置为成功
     * 替代core（资方）绑卡申请
     *
     * @param credit
     * @return
     */
    private BindCardRecord bindApplySubstitute(Credit credit) {
        CreditUserInfo creditUserInfo = creditUserInfoRepository.findByCreditId(credit.getId())
            .orElseThrow(() -> new BizException(ResultCode.USER_INFO_NOT_EXIST));

        // 初始化绑卡记录
        BindCardRecord bindCardRecord = initRecord(credit, creditUserInfo);
        //直接成功
        bindCardRecord.setAgreeNo(creditUserInfo.getCardAgreeNo());
        bindCardRecord.setMerchantNo(creditUserInfo.getCardMerchantNo());
        bindCardRecord.setState(ProcessState.SUCCEED);
        bindCardRecord.setChannel(creditUserInfo.getCardChannel());
        bindCardRecord.setRemark("不需要资方绑卡,直接成功");

        //保存用户卡
        UserBankCard userBankCard = createUserBankCard(bindCardRecord);
        userBankCardRepository.save(userBankCard);

        return recordRepository.save(bindCardRecord);
    }

    /**
     * 替代core（资方）绑卡确认
     *
     * @param bindCardRecord
     * @param credit
     * @return
     */
    private BindResultVo bindConfirmSubstitute(BindCardRecord bindCardRecord, Credit credit) {
        BindResultVo bindResultVo = CardConvert.INSTANCE.toVo(bindCardRecord);
        bindResultVo.setCreditId(credit.getId());
        bindResultVo.setOrderId(credit.getOrderId());
        return bindResultVo;
    }

    /**
     * 换绑卡-申请
     *
     * @param loan
     * @param exchangeCardApplyReq
     * @return
     */
    public BindCardRecord bindExchangeApply(Loan loan, ExchangeCardApplyReq exchangeCardApplyReq) {
        if (NO_BIND_CHANNELS.contains(loan.getBankChannel())) {
            return bindExchangeApplySubstitute(loan, exchangeCardApplyReq);
        }
        return bindExchangeApplyNormal(loan, exchangeCardApplyReq);
       // return bindExchangeApplySubstitute(loan, exchangeCardApplyReq);
    }

    /**
     * 换绑卡-资方共享协议号
     * <p>
     * 某些资方在贷后换绑后需要共享协议号
     */
    public void afterLoanShareCard(Loan loan) {
        if (loan.getBankChannel() != BankChannel.CYBK
        ) {
            return;
        }
        UserBankCard platformCard = userBankCardRepository.findUserBankCardById(loan.getRepayCardId());

        ExchangeCardApplyReq req = new ExchangeCardApplyReq();
        req.setBoundSide(BoundSide.CAPITAL);
        req.setCardName(platformCard.getCardName());
        req.setCardNo(platformCard.getCardNo());
        req.setBankCode(platformCard.getBankCode());
        req.setBankName(platformCard.getBankName());
        req.setPhone(platformCard.getPhone());
        req.setLoanId(loan.getId());
        bindExchangeApplyNormal(loan, req);
    }


    /**
     * 换绑卡-确认
     *
     * @param bindCardRecord
     * @param smsCode
     * @return
     */
    public BindCardRecord bindExchangeConfirm(BindCardRecord bindCardRecord, String smsCode) {
        BindCardRelation bindCardRelation = relationRepository.findByBindCardApplyId(bindCardRecord.getId())
            .orElseThrow(() -> new BizException(ResultCode.CARD_RECORD_RELATION_NOT_EXIST));
        Loan loan = loanRepository.findById(bindCardRelation.getRelatedId()).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));

        if (NO_BIND_CHANNELS.contains(loan.getBankChannel())) {
            return bindExchangeConfirmSubstitute(bindCardRecord, smsCode);
        }
        return bindExchangeConfirmNormal(bindCardRecord, loan, smsCode);
    }


    private BindCardRecord bindExchangeApplyNormal(Loan loan, ExchangeCardApplyReq exchangeCardApplyReq) {
        UserInfo userInfo = userInfoRepository.findById(loan.getUserId()).orElseThrow(() -> new BizException(ResultCode.USER_LOAN_CARD_NOT_EXIST));
        if (exchangeCardApplyReq.getBankCode() == null) {
            CardBin cardBin = queryCardBin(exchangeCardApplyReq.getCardNo());
            if (cardBin == null) {
                throw new BizException(ResultCode.CARD_NOT_SUPPORT);
            }
            String bankAbbr = cardBin.getBankAbbr();
            exchangeCardApplyReq.setBankCode(bankAbbr);
            exchangeCardApplyReq.setBankName(cardBin.getShortName());
        }

        BindCardRecord bindCardRecord = this.initRecord(loan, exchangeCardApplyReq, userInfo);
        BindApplyDto bindApplyDto = CardConvert.INSTANCE.toBindApply(exchangeCardApplyReq);
        bindApplyDto.setBankChannel(loan.getBankChannel());
        bindApplyDto.setSysLoanId(loan.getLoanRecordId());
        bindApplyDto.setSysCreditId(loan.getCreditId());
        bindApplyDto.setCertNo(bindCardRecord.getCertNo());
        bindApplyDto.setBindSignMode( BindSignMode.SHARE );
        bindApplyDto.setAgreementNo(bindCardRecord.getAgreeNo());
        bindApplyDto.setProtocolChannel(CardConvert.INSTANCE.toCoreProtocolChannel(bindCardRecord.getChannel()));
        logger.info("申请core绑卡,cardId:{}, 入参: {}", bindCardRecord.getId(), JsonUtil.toJsonString(bindApplyDto));
        RestResult<BindResultDto> bindRestResult = finBindService.bindApply(bindApplyDto);
        logger.info("core绑卡结果: {}", JsonUtil.toJsonString(bindRestResult));
        BindResultDto bindResultDto = bindRestResult.getData();
        if (!bindRestResult.isSuccess() || ProcessStatus.FAIL == bindResultDto.getStatus()) {
            String msg = !bindRestResult.isSuccess() ? bindRestResult.getMsg() : bindResultDto.getMsg();
            logger.error("申请core换绑卡异常:{}", msg);

            bindCardRecord.setFailReason(msg);
            bindCardRecord.setState(ProcessState.FAILED);
            recordRepository.save(bindCardRecord);

            throw new BizException(ResultCode.CARD_APPLY_FAIL);
        }

        bindCardRecord.setConfirmOrderNo(bindResultDto.getSysId());

        if (SignStatus.Y == bindResultDto.getSignStatus()) {
            // 已签约
            bindCardRecord.setState(ProcessState.SUCCEED);
            bindCardRecord.setAgreeNo(bindResultDto.getAgreementNo());

            UserBankCard userBankCard = createUserBankCard(bindCardRecord);
            UserBankCard save = userBankCardRepository.save(userBankCard);

            //保存还款卡信息
            loanRepository.findById(loan.getId()).ifPresent(record -> {
                record.setRepayCardId(save.getId());
                loanRepository.save(record);
            });
        } else {
            bindCardRecord.setState(ProcessState.PROCESSING);
        }
        return recordRepository.save(bindCardRecord);
    }

    /**
     * 替代core（资方）换绑卡申请
     *
     * @param loan
     * @param exchangeCardApplyReq
     * @return
     */
    private BindCardRecord bindExchangeApplySubstitute(Loan loan, ExchangeCardApplyReq exchangeCardApplyReq) {
        UserInfo userInfo = userInfoRepository.findById(loan.getUserId()).orElseThrow(() -> new BizException(ResultCode.USER_LOAN_CARD_NOT_EXIST));

        BindCardRecord bindCardRecord = this.initRecord(loan, exchangeCardApplyReq, userInfo);
        //直接成功
        bindCardRecord.setState(ProcessState.SUCCEED);
        bindCardRecord.setRemark("不需要资方绑卡,直接成功");
        //保存用户卡
        UserBankCard userBankCard = createUserBankCard(bindCardRecord);
        UserBankCard save = userBankCardRepository.save(userBankCard);

        //保存还款卡信息
        loanRepository.findById(loan.getId()).ifPresent(record -> {
            record.setRepayCardId(save.getId());
            loanRepository.save(record);
        });
        return recordRepository.save(bindCardRecord);
    }

    private BindCardRecord bindExchangeConfirmNormal(BindCardRecord bindCardRecord, Loan loan, String smsCode) {
        // 参数
        BindConfirmDto bindConfirmDto = new BindConfirmDto();
        bindConfirmDto.setBankChannel(loan.getBankChannel());
        bindConfirmDto.setBandCardId(bindCardRecord.getConfirmOrderNo());
        bindConfirmDto.setSms(smsCode);
        bindConfirmDto.setSysLoanId(loan.getId());
        bindConfirmDto.setSysCreditId(loan.getCreditId());
        // 请求
        logger.info("申请core绑卡确认,cardId:{} ,入参:{}", bindCardRecord.getId(), JsonUtil.toJsonString(bindConfirmDto));
        RestResult<BindResultDto> bindRestResult = finBindService.bindConfirm(bindConfirmDto);
        logger.info("申请core绑卡确认结果:{}", JsonUtil.toJsonString(bindRestResult));
        if (!bindRestResult.isSuccess() || ProcessStatus.FAIL == bindRestResult.getData().getStatus()) {
            String msg = !bindRestResult.isSuccess() ? bindRestResult.getMsg() : bindRestResult.getData().getMsg();
            logger.error("申请core换绑卡确认异常:{}", msg);

            throw new BizException(ResultCode.CARD_CONFIRM_FAIL);
        }
        bindCardRecord = getBindCardRecord(bindCardRecord, bindRestResult, bindConfirmDto.getBankChannel());
        return bindCardRecord;
    }

    /**
     * 替代core（资方）换绑卡确认
     *
     * @param bindCardRecord
     * @param smsCode
     * @return
     */
    private BindCardRecord bindExchangeConfirmSubstitute(BindCardRecord bindCardRecord, String smsCode) {

        // 公共绑卡确认
        //commonBindConfirm(bindCardRecord, smsCode);
        bindCardRecord = recordRepository.save(bindCardRecord);

        if (ProcessState.SUCCEED.equals(bindCardRecord.getState())) {
            UserBankCard userBankCard = createUserBankCard(bindCardRecord);
            userBankCardRepository.save(userBankCard);
        }


        return bindCardRecord;
    }

    private BindCardRecord initRecord(Credit credit, CreditUserInfo creditUserInfo) {
        BindCardRecord bindCardRecord = new BindCardRecord();
        bindCardRecord.setUserId(credit.getUserId());
        bindCardRecord.setBankCardNo(creditUserInfo.getCardNo());
        bindCardRecord.setCertNo(creditUserInfo.getCertNo());
        bindCardRecord.setPhone(creditUserInfo.getCardPhone());
        bindCardRecord.setName(creditUserInfo.getCardName());
        bindCardRecord.setBankCode(creditUserInfo.getCardBankCode());
        bindCardRecord.setBankName(creditUserInfo.getCardBankName());
        bindCardRecord.setChannel(creditUserInfo.getCardChannel());
        bindCardRecord.setState(ProcessState.INIT);
        bindCardRecord.setBoundSide(BoundSide.CAPITAL);
        bindCardRecord = recordRepository.save(bindCardRecord);
        // bindCardRelation
        BindCardRelation bindCardRelation = new BindCardRelation();
        bindCardRelation.setRelatedId(credit.getId());
        bindCardRelation.setBindStage(LoanStage.CREDIT);
        bindCardRelation.setBindCardApplyId(bindCardRecord.getId());
        bindCardRelation.setUserId(credit.getUserId());
        relationRepository.save(bindCardRelation);

        return bindCardRecord;
    }

    private BindCardRecord initRecord(Loan loan, ExchangeCardApplyReq exchangeCardApplyReq, UserInfo userInfo) {

        BindCardRecord bindCardRecord = getBindCardRecord(loan, exchangeCardApplyReq, userInfo);
        bindCardRecord = recordRepository.save(bindCardRecord);
        BindCardRelation bindCardRelation = new BindCardRelation();
        bindCardRelation.setRelatedId(loan.getId());
        bindCardRelation.setBindStage(LoanStage.REPAY);
        bindCardRelation.setBindCardApplyId(bindCardRecord.getId());
        bindCardRelation.setUserId(loan.getUserId());
        relationRepository.save(bindCardRelation);
        return bindCardRecord;

    }

    private BindCardRecord getBindCardRecord(BindCardRecord bindCardRecord, RestResult<BindResultDto> bindRestResult, BankChannel bankChanel) {
        BindResultDto bindResultDto = bindRestResult.getData();
        bindCardRecord.setState(EnumConvert.INSTANCE.toCashState(bindResultDto.getStatus()));
        bindCardRecord.setFailReason(bindResultDto.getMsg());

        bindCardRecord = recordRepository.save(bindCardRecord);
        if (ProcessState.SUCCEED.equals(bindCardRecord.getState())) {
            // 绑卡成功, 插入用户绑卡记录
            UserBankCard userBankCard = new UserBankCard();
            userBankCard.setUserId(bindCardRecord.getUserId());
            userBankCard.setCardNo(bindCardRecord.getBankCardNo());
            userBankCard.setCardName(bindCardRecord.getName());
            userBankCard.setPhone(bindCardRecord.getPhone());
            userBankCard.setCertNo(bindCardRecord.getCertNo());
            userBankCard.setBankCode(bindCardRecord.getBankCode());
            userBankCard.setBankName(bindCardRecord.getBankName());
            userBankCard.setChannel(bindCardRecord.getChannel());
            userBankCard.setMerchantNo(bindCardRecord.getMerchantNo());
            userBankCard.setAgreeNo(bindCardRecord.getAgreeNo());
            userBankCard.setId(bindCardRecord.getId());
            userBankCard.setBoundSide(bindCardRecord.getBoundSide());
            userBankCardRepository.save(userBankCard);
        }
        return bindCardRecord;
    }

    private BindCardRecord getBindCardRecord(Loan loan, ExchangeCardApplyReq exchangeCardApplyReq, UserInfo userInfo) {
        //查询用户已换绑的平台卡
        UserBankCard userPlatFormBankCard = userBankCardRepository.findUserBankCardById(loan.getRepayCardId());

        BindCardRecord bindCardRecord = new BindCardRecord();
        bindCardRecord.setUserId(userPlatFormBankCard.getUserId());
        bindCardRecord.setBankCardNo(exchangeCardApplyReq.getCardNo());
        bindCardRecord.setCertNo(exchangeCardApplyReq.getIdNo());
        bindCardRecord.setPhone(exchangeCardApplyReq.getPhone());
        bindCardRecord.setName(exchangeCardApplyReq.getCardName());
        bindCardRecord.setBankCode(exchangeCardApplyReq.getBankCode());
        bindCardRecord.setChannel(userPlatFormBankCard.getChannel());
        bindCardRecord.setState(ProcessState.INIT);
        bindCardRecord.setBoundSide(exchangeCardApplyReq.getBoundSide());
        bindCardRecord.setBankName(exchangeCardApplyReq.getBankName());
        bindCardRecord.setMerchantNo(userPlatFormBankCard.getMerchantNo());
        bindCardRecord.setAgreeNo(exchangeCardApplyReq.getAgreeNo());
        return bindCardRecord;
    }


    private UserBankCard createUserBankCard(BindCardRecord bindCardRecord) {
        UserBankCard userBankCard = new UserBankCard();
        userBankCard.setUserId(bindCardRecord.getUserId());
        userBankCard.setCardNo(bindCardRecord.getBankCardNo());
        userBankCard.setCardName(bindCardRecord.getName());
        userBankCard.setPhone(bindCardRecord.getPhone());
        userBankCard.setCertNo(bindCardRecord.getCertNo());
        userBankCard.setBankCode(bindCardRecord.getBankCode());
        userBankCard.setBankName(bindCardRecord.getBankName());
        userBankCard.setChannel(bindCardRecord.getChannel());
        userBankCard.setMerchantNo(bindCardRecord.getMerchantNo());
        userBankCard.setAgreeNo(bindCardRecord.getAgreeNo());
        userBankCard.setId(bindCardRecord.getId());
        userBankCard.setBoundSide(bindCardRecord.getBoundSide());
        userBankCard.setRemark(bindCardRecord.getRemark());
        return userBankCard;
    }

    @Nullable
    public CardBin queryCardBin(String cardNo) {
        CardBin cardBin = localCardBinService.query(cardNo);
        if (Objects.isNull(cardBin) || StringUtils.isBlank(cardBin.getBankAbbr())) {
            // 查询阿里云
            cardBin = alipayCardBinService.query(cardNo);
            if (cardBin != null) {
                logger.info("cardBin本地未查到，alipay查询到结果，卡号：" + cardNo + " 银行：" + cardBin.getBankAbbr());
                return cardBin;
            }
        }
        return cardBin;
    }







    @Autowired
    public void setCommonRequestService(CommonRequestService commonRequestService) {
        this.commonRequestService = commonRequestService;
    }

    @Autowired
    public void setRecordRepository(BindCardRecordRepository recordRepository) {
        this.recordRepository = recordRepository;
    }

    @Autowired
    public void setRelationRepository(BindCardRelationRepository relationRepository) {
        this.relationRepository = relationRepository;
    }

    @Autowired
    public void setUserBankCardRepository(UserBankCardRepository userBankCardRepository) {
        this.userBankCardRepository = userBankCardRepository;
    }

    @Autowired
    public void setFinBindService(FinBindService finBindService) {
        this.finBindService = finBindService;
    }

    @Autowired
    public void setCreditUserInfoRepository(CreditUserInfoRepository creditUserInfoRepository) {
        this.creditUserInfoRepository = creditUserInfoRepository;
    }

    @Autowired
    public void setCreditRepository(CreditRepository creditRepository) {
        this.creditRepository = creditRepository;
    }


}
