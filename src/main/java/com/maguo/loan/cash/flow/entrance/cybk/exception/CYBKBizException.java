package com.maguo.loan.cash.flow.entrance.cybk.exception;


import com.maguo.loan.cash.flow.entrance.cybk.enums.CYBKResultCode;

import java.io.Serial;

/**
 * 自定异常类
 */
public class CYBKBizException extends RuntimeException {

    @Serial
    private static final long serialVersionUID = 1431155259402142272L;

    private final CYBKResultCode resultCode;

    public CYBKBizException(String message) {
        super(message);
        this.resultCode = CYBKResultCode.SYSTEM_ERROR;
    }

    public CYBKBizException(CYBKResultCode resultCode) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
    }

    public CYBKBizException(String message, CYBKResultCode resultCode) {
        super(message);
        this.resultCode = resultCode;
    }

    public CYBKResultCode getResultCode() {
        return resultCode;
    }

}
