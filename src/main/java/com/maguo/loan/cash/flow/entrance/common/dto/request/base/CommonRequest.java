package com.maguo.loan.cash.flow.entrance.common.dto.request.base;

import java.io.Serial;
import java.io.Serializable;

/**
 * 商城请求公共参数
 *
 * <AUTHOR>
 */
public class CommonRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 3563204549622780138L;

    /**
     * 优品分配
     */
    private String orgCode;

    /**
     * 优品分配
     */
    private String productCode;

    /**
     * 请求时间
     */
    private String transDate;

    /**
     * 请求流水号，长度不超过50位
     */
    private String transSeq;

    /**
     * des加密后的业务参数
     */
    private String data;

    /**
     * RSA签名
     */
    private String signature;

    /**
     * RSA加密后的desKey
     */
    private String desKey;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTransSeq() {
        return transSeq;
    }

    public void setTransSeq(String transSeq) {
        this.transSeq = transSeq;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getDesKey() {
        return desKey;
    }

    public void setDesKey(String desKey) {
        this.desKey = desKey;
    }
}
