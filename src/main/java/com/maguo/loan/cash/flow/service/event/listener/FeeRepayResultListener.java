package com.maguo.loan.cash.flow.service.event.listener;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteeRecord;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.FeeType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.SmsTemplate;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteeRecordRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.SmsService;
import com.maguo.loan.cash.flow.service.event.FeeRepayResultEvent;
import com.maguo.loan.cash.flow.service.event.RepaySucceedResultEvent;
import com.maguo.loan.cash.flow.util.AmountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 费用还款结果事件
 */
@Component
public class FeeRepayResultListener {

    private static final Logger logger = LoggerFactory.getLogger(FeeRepayResultListener.class);

    @Autowired
    private RepayExtraGuaranteeRecordRepository feeRecordRepository;

    @Autowired
    private RepayExtraGuaranteePlanRepository feePlanRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private SmsService smsService;

    @Autowired
    private MqService mqService;



    @EventListener(FeeRepayResultEvent.class)
    public void feeChargeResult(FeeRepayResultEvent event) {
        // 费用记录 & 费用计划
        RepayExtraGuaranteeRecord feeRecord
            = feeRecordRepository.findById(event.getRecordId()).orElseThrow(() -> new BizException(ResultCode.REPAY_FEE_NOT_EXIST));
        RepayExtraGuaranteePlan plan
            = feePlanRepository.findById(feeRecord.getRepayPlanId()).orElseThrow(() -> new BizException(ResultCode.REPAY_PLAN_NOT_EXIST));

        // 对客还款记录
        CustomRepayRecord customRepayRecord
            = customRepayRecordRepository.findById(plan.getRepayRecordId()).orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));


        if (!event.getRepayState().isFinal()) {
            // 不处理非终态
            return;
        }

        if (ProcessState.FAILED.equals(event.getRepayState())) {
            feeRecord.setRepayState(ProcessState.FAILED);
            feeRecord.setFailReason(event.getFailReason());
            feeRecordRepository.save(feeRecord);
            Loan loan = loanRepository.findById(customRepayRecord.getLoanId()).orElseThrow();

            if (FlowChannel.isRepaySendSms(loan.getFlowChannel()) && feeRecord.getRepayPurpose().equals(RepayPurpose.CLEAR)) {
                //提前结清操作失败发送短信
                UserInfo userInfo = userInfoRepository.findById(loan.getUserId()).orElseThrow();
//                smsService.send(SmsTemplate.REPAY_CLEAR_FAILED, Map.of("name", userInfo.getName(), "flowChannel",
//                    loan.getFlowChannel().name()), userInfo.getMobile());
            }
            //还款回调
            CallBackDTO callBackDTO = new CallBackDTO();
            callBackDTO.setFlowChannel(loan.getFlowChannel());
            callBackDTO.setCallbackState(CallbackState.REPAY_FAIL);
            callBackDTO.setBusinessId(customRepayRecord.getId());
            mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
            //催收消息发送
           // mqService.submitPlatRepay(customRepayRecord.getId());
            return;
        }

        // 费用记录
        feeRecord.setRepayState(ProcessState.SUCCEED);
        feeRecord.setRepaidTime(LocalDateTime.now());

        if (feeRecord.getRepayMode() == RepayMode.ONLINE) {
            feeRecord.setActGuaranteeAmt(plan.getPlanGuaranteeAmt());
            feeRecord.setActConsultAmt(plan.getPlanConsultAmt());
            feeRecord.setActPlatformPenaltyAmt(plan.getPlanPlatformPenaltyAmt());
        }
        feeRecordRepository.save(feeRecord);
        // 费用计划
        plan.setPaidAmount(AmountUtil.sum(plan.getPaidAmount(), feeRecord.getGuaranteeAmt()));
        plan.setLeftAmount(BigDecimal.ZERO);
        plan.setActRepayTime(LocalDateTime.now());
        plan.setPlanState(RepayState.REPAID);
        feePlanRepository.save(plan);

        // 对客状态
        customSuccess(feeRecord, customRepayRecord);

        // 还款结果事件
        eventPublisher.publishEvent(new RepaySucceedResultEvent(customRepayRecord.getId()));
    }

    private void customSuccess(RepayExtraGuaranteeRecord feeRecord, CustomRepayRecord customRepayRecord) {
        // 还款计划
        if (RepayPurpose.CURRENT.equals(feeRecord.getRepayPurpose())) {
            // 当期
            RepayPlan currentPlan = repayPlanRepository.findByLoanIdAndPeriod(customRepayRecord.getLoanId(), customRepayRecord.getPeriod());
            currentPlan.setActConsultFee(customRepayRecord.getConsultFee());
            //实还平台罚息
            currentPlan.setActPenaltyAmt(customRepayRecord.getPenaltyAmt());
            currentPlan.setActRepayTime(LocalDateTime.now());
            currentPlan.setCustRepayState(RepayState.REPAID);

            //振兴银行 融担费+咨询费
            if (FeeType.GUARANTEE_CONSULT == feeRecord.getFeeType()) {
                currentPlan.setActGuaranteeAmt(customRepayRecord.getGuaranteeAmt());
            }

            currentPlan.setActAmount(AmountUtil.sum(currentPlan.getActPrincipalAmt(), currentPlan.getActInterestAmt(), currentPlan.getActGuaranteeAmt(),
                currentPlan.getActConsultFee(), currentPlan.getActPenaltyAmt()));
            repayPlanRepository.save(currentPlan);
        }

        if (RepayPurpose.CLEAR.equals(feeRecord.getRepayPurpose())) {
            List<RepayPlan> repayPlans = repayPlanRepository.findByLoanIdOrderByPeriod(feeRecord.getLoanId());
            repayPlans.stream().filter(p -> p.getPeriod().compareTo(feeRecord.getPeriod()) >= 0).collect(Collectors.toList()).forEach(cp -> {
                if (cp.getPeriod().compareTo(feeRecord.getPeriod()) == 0) {
                    if (FeeType.GUARANTEE.equals(feeRecord.getFeeType())) {
                        // 平台结清融担费 本息结清包含的融担费 + 二次结清融担费
                        cp.setActGuaranteeAmt(AmountUtil.sum(cp.getActGuaranteeAmt(), feeRecord.getGuaranteeAmt()));
                        cp.setActConsultFee(BigDecimal.ZERO);
                    }
                    if (FeeType.CONSULT.equals(feeRecord.getFeeType())) {
                        // 结清咨询费
                        cp.setActConsultFee(feeRecord.getGuaranteeAmt());
                    }
                    if (FeeType.GUARANTEE_CONSULT.equals(feeRecord.getFeeType())) {
                        cp.setActConsultFee(customRepayRecord.getConsultFee());
                        cp.setActGuaranteeAmt(customRepayRecord.getGuaranteeAmt());
                    }
                    cp.setActAmount(AmountUtil.sum(cp.getActPrincipalAmt(), cp.getActInterestAmt(), cp.getActGuaranteeAmt(),
                        cp.getActConsultFee(), cp.getActPenaltyAmt()));
                } else {
                    cp.setActPrincipalAmt(BigDecimal.ZERO);
                    cp.setActInterestAmt(BigDecimal.ZERO);
                    cp.setActGuaranteeAmt(BigDecimal.ZERO);
                    cp.setActConsultFee(BigDecimal.ZERO);
                    cp.setActPenaltyAmt(BigDecimal.ZERO);
                    cp.setActBreachAmt(BigDecimal.ZERO);
                    cp.setActAmount(BigDecimal.ZERO);
                }

                // save
                cp.setActRepayTime(LocalDateTime.now());
                cp.setCustRepayState(RepayState.REPAID);
                repayPlanRepository.save(cp);
            });
        }

        // 对客记录
        customRepayRecord.setRepaidDate(LocalDateTime.now());
        customRepayRecord.setRepayState(ProcessState.SUCCEED);
        customRepayRecordRepository.save(customRepayRecord);
    }

}
