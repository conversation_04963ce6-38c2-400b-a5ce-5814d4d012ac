package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.FqlWithholdDetail;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/8/6 16:26
 */
public interface FqlWithholdDetailRepository extends JpaRepository<FqlWithholdDetail, String> {

    Optional<FqlWithholdDetail> findByFqlRepayApplyRecordId(String fqlRepayApplyRecordId);

}
