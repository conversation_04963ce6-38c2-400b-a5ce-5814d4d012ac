package com.maguo.loan.cash.flow.entrance.common.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 借款查询响应
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
public class QueryLoanResDTO {

    /**
     * 合作机构单号
     */
    private String partnerOrderNo;

    /**
     * 我方单号
     */
    private String orderId;

    /**
     * 订单状态
     */
    private String orderState;

    private String bankChannelName;
    /**
     * 期次
     */
    private Integer period;

    /**
     * 利率
     */
    private BigDecimal rate;

    /**
     * 银行卡id
     */
    private String bankCardId;

    /**
     * 银行卡号
     */
    private String bankCardNo;
    /**
     * 银行名字
     */
    private String bankName;

    /**
     * 放款成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
    /**
     * 放款金额
     */
    private BigDecimal loanAmount;
    /**
     * 放款失败原因
     */
    private String rejectReason;
    /**
     * 冻结期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime freezeTime;

    /**
     * 是否购买权益
     */
    private String rightsMarking;
    /**
     * 权益金额
     */
    private BigDecimal rightsAmount;
    /**
     * 计划权益扣款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate shouldRepayDate;

    /**
     * 是否是复借用户
     * Y- 是
     * N- 不是
     */
    private String reLoan;


    public String getBankChannelName() {
        return bankChannelName;
    }

    public void setBankChannelName(String bankChannelName) {
        this.bankChannelName = bankChannelName;
    }

    public String getReLoan() {
        return reLoan;
    }

    public void setReLoan(String reLoan) {
        this.reLoan = reLoan;
    }

    public BigDecimal getRightsAmount() {
        return rightsAmount;
    }

    public void setRightsAmount(BigDecimal rightsAmount) {
        this.rightsAmount = rightsAmount;
    }

    public LocalDate getShouldRepayDate() {
        return shouldRepayDate;
    }

    public void setShouldRepayDate(LocalDate shouldRepayDate) {
        this.shouldRepayDate = shouldRepayDate;
    }

    public String getRightsMarking() {
        return rightsMarking;
    }

    public void setRightsMarking(String rightsMarking) {
        this.rightsMarking = rightsMarking;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public String getBankCardId() {
        return bankCardId;
    }

    public void setBankCardId(String bankCardId) {
        this.bankCardId = bankCardId;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public LocalDateTime getFreezeTime() {
        return freezeTime;
    }

    public void setFreezeTime(LocalDateTime freezeTime) {
        this.freezeTime = freezeTime;
    }
}
