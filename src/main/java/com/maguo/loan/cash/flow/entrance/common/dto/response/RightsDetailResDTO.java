package com.maguo.loan.cash.flow.entrance.common.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 权益详情
 *
 * <AUTHOR>
 * @date 2024/9/10
 */
public class RightsDetailResDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 7307584637199782805L;

    /**
     * 合作机构单号
     */
    private String partnerOrderNo;
    /**
     * 是否可以购买权益
     */
    private Boolean rightsSwitch;

    /**
     * 权益包扣款日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate date;
    /**
     * 权益包金额
     */
    private BigDecimal price;
    /**
     * 银行名
     */
    private String cardName;
    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 最高节省金额
     */
    private BigDecimal maxEconomizeAmount;

    /**
     * 权益详情链接
     */
    private String rightsInfoUrl;


    public String getRightsInfoUrl() {
        return rightsInfoUrl;
    }

    public void setRightsInfoUrl(String rightsInfoUrl) {
        this.rightsInfoUrl = rightsInfoUrl;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public BigDecimal getMaxEconomizeAmount() {
        return maxEconomizeAmount;
    }

    public void setMaxEconomizeAmount(BigDecimal maxEconomizeAmount) {
        this.maxEconomizeAmount = maxEconomizeAmount;
    }

    public Boolean getRightsSwitch() {
        return rightsSwitch;
    }

    public void setRightsSwitch(Boolean rightsSwitch) {
        this.rightsSwitch = rightsSwitch;
    }
}
