package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.FqlRebindRecord;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/8/7 16:26
 */
public interface FqlRebindRecordRepository extends JpaRepository<FqlRebindRecord, String> {

    FqlRebindRecord findByCreditIdAndStateAndBoundSide(String creditId, ProcessState state, BoundSide boundSide);

}
