package com.maguo.loan.cash.flow.entrance.common.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * des 加解密工具类
 */
public class DesUtil {

    public static final int DEFAULT_KEY_SIZE = 56;
    public static final String DES_ALGORITHM = "DES";
    public static final String DES_TRANSFORMATION = "DES/ECB/PKCS5Padding";


    /**
     * 生成秘钥base64编码
     *
     * @return base64编码的密钥
     * @throws Exception 异常
     */
    public static String generateKeyBase64() throws Exception {
        // 以DES的方式初始化Key生成器
        KeyGenerator keyGenerator = KeyGenerator.getInstance(DES_ALGORITHM);
        keyGenerator.init(DEFAULT_KEY_SIZE);
        // 生成一个Key
        SecretKey generateKey = keyGenerator.generateKey();
        // 转变为字节数组
        byte[] encoded = generateKey.getEncoded();
        // 生成密钥字符串
        return Base64.getEncoder().encodeToString(encoded);
    }

    /**
     * 加密
     *
     * @param data 明文字符串
     * @param key  base64密钥
     * @return 加密后base64字符串
     * @throws Exception 异常
     */
    public static String encryptBase64(String data, String key) throws Exception {
        // 再把我们的字符串转变为字节数组，可以用于另一方使用，验证
        byte[] decodeHex = Base64.getDecoder().decode(key);
        // 生成密钥对象
        SecretKeySpec secretKeySpec = new SecretKeySpec(decodeHex, DES_ALGORITHM);

        // 获取加解密实例
        Cipher cipher = Cipher.getInstance(DES_TRANSFORMATION);
        // 初始化加密模式
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        // 加密
        byte[] resultArr = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(resultArr);
    }

    /**
     * 解密
     *
     * @param data 加密base64密文
     * @param key  base64密钥
     * @return 明文
     * @throws Exception 异常
     */
    public static String decryptBase64(String data, String key) throws Exception {
        byte[] decodeHex = Base64.getDecoder().decode(key);
        // 生成密钥对象
        SecretKeySpec secretKeySpec = new SecretKeySpec(decodeHex, DES_ALGORITHM);

        // 获取加解密实例
        Cipher cipher = Cipher.getInstance(DES_TRANSFORMATION);

        // 初始化解密模式
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        // 解密
        byte[] resultArr = cipher.doFinal(Base64.getDecoder().decode(data));

        return new String(resultArr, StandardCharsets.UTF_8);
    }
}
