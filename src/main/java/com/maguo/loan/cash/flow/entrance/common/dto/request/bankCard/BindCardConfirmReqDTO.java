package com.maguo.loan.cash.flow.entrance.common.dto.request.bankCard;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/8/23
 */
public class BindCardConfirmReqDTO {

    /**
     * 绑卡确认时传递的流水号标识
     */
    @NotBlank
    private String confirmNo;

    /**
     * 验证码
     */
    @NotBlank
    private String smsCode;

    /**
     * 合作机构订单号
     */
    @NotBlank
    private String partnerOrderNo;

    public String getConfirmNo() {
        return confirmNo;
    }

    public void setConfirmNo(String confirmNo) {
        this.confirmNo = confirmNo;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }
}
