package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.FqlRepayApplyRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/8/6 16:26
 */
public interface FqlRepayApplyRecordRepository extends JpaRepository<FqlRepayApplyRecord, String> {

    boolean existsByWithholdSerialNo(String withholdSerialNo);

    FqlRepayApplyRecord findByWithholdSerialNoAndPartnerCode(String withholdSerialNo, String partnerCode);

    @Query("select l from FqlRepayApplyRecord l where l.withholdSerialNo = ?1 ")
    Optional<FqlRepayApplyRecord> findByWithholdSerialNo(String outRepayId);

}
