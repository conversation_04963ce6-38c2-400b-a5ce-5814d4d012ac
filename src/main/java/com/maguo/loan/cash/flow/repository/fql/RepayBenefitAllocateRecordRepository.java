package com.maguo.loan.cash.flow.repository.fql;

import com.maguo.loan.cash.flow.entity.ppd.RepayBenefitAllocateRecord;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 还款分润记录表
 *
 * <AUTHOR>
 * @date 2025-08-11 16:21
 */
public interface RepayBenefitAllocateRecordRepository extends JpaRepository<RepayBenefitAllocateRecord, String> {
    boolean findByCustomerRepayRecordId(String id);
}
