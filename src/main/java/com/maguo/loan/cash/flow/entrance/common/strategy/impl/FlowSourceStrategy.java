package com.maguo.loan.cash.flow.entrance.common.strategy.impl;

import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;

import java.util.Map;

public class FlowSourceStrategy implements ProductCodeStrategy {
    @Override
    public boolean supports(String source) {
        return "FLOW".equalsIgnoreCase(source);
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String flowSource = params.get("flowSource");
        if (flowSource == null) {
            throw new IllegalArgumentException("流量侧映射必须包含 'flowSource' 参数");
        }

        switch (flowSource.toUpperCase()) {
            case "LVXIN":
                return params.get("productType");
            case "PPD":
                String sourceCode = params.get("sourceCode");
                String simplifiedSourceCode = sourceCode.replaceAll("\\d$", "");
                return simplifiedSourceCode + "_" + params.get("accessType");
            case "FQL":
                return params.get("partnerCode");
            default:
                throw new RuntimeException("不支持的流量来源: " + flowSource);
        }
    }
}
