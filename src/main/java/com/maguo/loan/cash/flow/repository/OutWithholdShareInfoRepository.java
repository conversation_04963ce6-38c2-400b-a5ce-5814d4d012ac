package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.OutWithholdShareInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/8/7 13:50
 */
public interface OutWithholdShareInfoRepository extends JpaRepository<OutWithholdShareInfo, String> {

    List<OutWithholdShareInfo> findByOutWithholdFlowId(String outWithholdFlowId);
}
