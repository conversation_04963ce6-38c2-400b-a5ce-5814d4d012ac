package com.maguo.loan.cash.flow.service.bound.exchange;

import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.vo.BaseRequest;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/10/12
 * 换绑申请
 */
public class ExchangeCardApplyReq extends BaseRequest {

    /**
     * 绑卡方
     */
    private BoundSide boundSide;

    /**
     * 户名
     */
    private String cardName;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 手机号
     */
    private String phone;

    @NotBlank(message = "借据不能为空")
    private String loanId;

    private String agreeNo;

    private String idNo;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public BoundSide getBoundSide() {
        return boundSide;
    }

    public void setBoundSide(BoundSide boundSide) {
        this.boundSide = boundSide;
    }

    public String getAgreeNo() {
        return agreeNo;
    }

    public void setAgreeNo(String agreeNo) {
        this.agreeNo = agreeNo;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }
}
