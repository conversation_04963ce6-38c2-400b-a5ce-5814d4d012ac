package com.maguo.loan.cash.flow.entrance.common.dto.request.callback;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.entrance.common.dto.request.base.CommonApiRequest;
import com.maguo.loan.cash.flow.entrance.common.enums.RepayResult;
import com.maguo.loan.cash.flow.entrance.common.enums.api.CallBackApi;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayPurpose;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
public class RepayPushReqDTO implements CommonApiRequest {

    /**
     * 还款流水标识
     */
    private String repayId;

    /**
     * 期次
     */
    private Integer period;

    private RepayMode repayMode;

    private RepayPurpose repayPurpose;

    private String orderId;

    /**
     * 合作机构单号
     */
    private String partnerOrderNo;

    /**
     * 合作机构流水号
     */
    private String partnerRepayNo;

    /**
     * 还款结果
     */
    private RepayResult repayState;

    /**
     * 实还款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime actRepayTime;
    /**
     * 实还款本金
     */
    private BigDecimal actPrincipalAmt = BigDecimal.ZERO;
    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt = BigDecimal.ZERO;
    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt = BigDecimal.ZERO;
    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeAmt = BigDecimal.ZERO;
    /**
     * 实还咨询费
     */
    private BigDecimal actConsultFee = BigDecimal.ZERO;

    /**
     * 实还总额
     */
    private BigDecimal actAmount = BigDecimal.ZERO;

    public String getRepayId() {
        return repayId;
    }

    public void setRepayId(String repayId) {
        this.repayId = repayId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getPartnerRepayNo() {
        return partnerRepayNo;
    }

    public void setPartnerRepayNo(String partnerRepayNo) {
        this.partnerRepayNo = partnerRepayNo;
    }

    public RepayResult getRepayState() {
        return repayState;
    }

    public void setRepayState(RepayResult repayState) {
        this.repayState = repayState;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActGuaranteeAmt() {
        return actGuaranteeAmt;
    }

    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }

    public BigDecimal getActConsultFee() {
        return actConsultFee;
    }

    public void setActConsultFee(BigDecimal actConsultFee) {
        this.actConsultFee = actConsultFee;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    @Override
    public CallBackApi getApiType() {
        return CallBackApi.REPAY_PUSH;
    }
}
