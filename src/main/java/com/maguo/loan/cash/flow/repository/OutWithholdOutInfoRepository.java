package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.OutWithholdOutInfo;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @version 1.0
 * @title:
 * @desc:
 * @date 2025/8/7 13:52
 */
public interface OutWithholdOutInfoRepository extends JpaRepository<OutWithholdOutInfo, String> {

    OutWithholdOutInfo findByOutWithholdFlowId(String outWithholdFlowId);
}
