package com.maguo.loan.cash.flow.service.event;

import com.maguo.loan.cash.flow.enums.SmsTemplate;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
public class SmsSendEvent extends ApplicationEvent {

    /**
     * 短信模板
     */
    private SmsTemplate smsTemplate;
    /**
     * 内容参数
     */
    private Map<String, String> contentParams;
    /**
     * 接收人手机号
     */
    private String receiverPhone;

    public SmsSendEvent(Object source, SmsTemplate smsTemplate, Map<String, String> contentParams, String receiverPhone) {
        super(source);
        this.smsTemplate = smsTemplate;
        this.contentParams = contentParams;
        this.receiverPhone = receiverPhone;
    }

    public SmsTemplate getSmsTemplate() {
        return smsTemplate;
    }

    public Map<String, String> getContentParams() {
        return contentParams;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }
}
