package com.maguo.loan.cash.flow.entrance.common.dto.response.base;


import com.maguo.loan.cash.flow.common.ResultCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

/**
 * 商城响应公共参数
 *
 * <AUTHOR>
 */
public class CommonResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -6801453934966403410L;
    /**
     * 响应code
     */
    private String respCode;

    /**
     * 响应信息
     */
    private String respMsg;

    /**
     * 交易时间
     */
    private String transDate;

    /**
     * 交易流水号
     */
    private String transSeq;

    /**
     * des加密后的业务参数
     */
    private String data;

    /**
     * RSA签名
     */
    private String signature;

    public boolean isSuccess() {
        return Objects.equals(ResultCode.SUCCESS.getCode(), this.respCode);
    }

    public boolean isFail() {
        return !ResultCode.SUCCESS.getCode().equals(respCode);
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTransSeq() {
        return transSeq;
    }

    public void setTransSeq(String transSeq) {
        this.transSeq = transSeq;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
