package com.maguo.loan.cash.flow.entrance.common.dto.response;

/**
 * 公共对接流量配置
 * <AUTHOR>
 */
public class PartnerConfigResDTO {

    /**
     * 流量渠道
     */
    private String flowChannel;

    /**
     * 域名
     */
    private String partnerDomainName;

    /**
     * 公司代码
     */
    private String partnerOrgCode;

    /**
     * 产品代码
     */
    private String partnerProductCode;

    /**
     * 合作机构名称
     */
    private String partnerName;

    /**
     * 合作机构公钥
     */
    private String partnerPublicKey;

    /**
     * 合作机构私钥
     */
    private String partnerPrivateKey;

    /**
     * 我方公钥
     */
    private String serverPublicKey;

    /**
     * 我方私钥
     */
    private String serverPrivateKey;

    public String getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getPartnerDomainName() {
        return partnerDomainName;
    }

    public void setPartnerDomainName(String partnerDomainName) {
        this.partnerDomainName = partnerDomainName;
    }

    public String getPartnerOrgCode() {
        return partnerOrgCode;
    }

    public void setPartnerOrgCode(String partnerOrgCode) {
        this.partnerOrgCode = partnerOrgCode;
    }

    public String getPartnerProductCode() {
        return partnerProductCode;
    }

    public void setPartnerProductCode(String partnerProductCode) {
        this.partnerProductCode = partnerProductCode;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerPublicKey() {
        return partnerPublicKey;
    }

    public void setPartnerPublicKey(String partnerPublicKey) {
        this.partnerPublicKey = partnerPublicKey;
    }

    public String getPartnerPrivateKey() {
        return partnerPrivateKey;
    }

    public void setPartnerPrivateKey(String partnerPrivateKey) {
        this.partnerPrivateKey = partnerPrivateKey;
    }

    public String getServerPublicKey() {
        return serverPublicKey;
    }

    public void setServerPublicKey(String serverPublicKey) {
        this.serverPublicKey = serverPublicKey;
    }

    public String getServerPrivateKey() {
        return serverPrivateKey;
    }

    public void setServerPrivateKey(String serverPrivateKey) {
        this.serverPrivateKey = serverPrivateKey;
    }
}
