package com.maguo.loan.cash.flow.service.banks;


import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.FeeType;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.util.AmountUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/30
 */
public abstract class AbstractBankRepayService implements IBankService {

    private LoanRepository loanRepository;
    private RepayPlanRepository repayPlanRepository;
    private RepayExtraGuaranteePlanRepository repayExtraGuaranteePlanRepository;
    private CustomRepayRecordRepository customRepayRecordRepository;

    public RepayExtraGuaranteePlan generateFeePlan(CustomRepayRecord customRepayRecord) {
        Loan loan = loanRepository.findById(customRepayRecord.getLoanId()).orElseThrow();
        //
        RepayExtraGuaranteePlan repayExtraGuaranteePlan = new RepayExtraGuaranteePlan();
        repayExtraGuaranteePlan.setRepayRecordId(customRepayRecord.getId());
        repayExtraGuaranteePlan.setUserId(loan.getUserId());
        repayExtraGuaranteePlan.setLoanId(loan.getId());
        repayExtraGuaranteePlan.setPeriod(customRepayRecord.getPeriod());
        repayExtraGuaranteePlan.setPlanRepayDate(LocalDate.now());
        repayExtraGuaranteePlan.setRepayPurpose(customRepayRecord.getRepayPurpose());
        repayExtraGuaranteePlan.setPaidAmount(BigDecimal.ZERO);
        repayExtraGuaranteePlan.setPlanState(RepayState.NORMAL);

        if (RepayPurpose.CURRENT.equals(customRepayRecord.getRepayPurpose())) {
            RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(customRepayRecord.getLoanId(), customRepayRecord.getPeriod());
            //剩余平台违约金 = 总罚息 - 资方罚息
            BigDecimal platformBreachAmt = AmountUtil.subtract(repayPlan.getPenaltyAmt(), repayPlan.getActCapitalPenaltyAmt());
            // 当期生成咨询费计划

            repayExtraGuaranteePlan.setFeeType(FeeType.CONSULT);

            BigDecimal planAmount = AmountUtil.sum(repayPlan.getConsultFee(), platformBreachAmt);
            repayExtraGuaranteePlan.setPlanAmount(planAmount);
            repayExtraGuaranteePlan.setLeftAmount(planAmount);

            repayExtraGuaranteePlan.setPlanGuaranteeAmt(BigDecimal.ZERO);

            repayExtraGuaranteePlan.setPlanConsultAmt(repayPlan.getConsultFee());
            repayExtraGuaranteePlan.setPlanPlatformPenaltyAmt(platformBreachAmt);

            return repayExtraGuaranteePlanRepository.save(repayExtraGuaranteePlan);
        } else {

            //todo 收剩余所有咨询费

            // 结清费用计划
            return generateClearFeePlan(repayExtraGuaranteePlan);

        }
    }

    public RepayExtraGuaranteePlan generateClearFeePlan(RepayExtraGuaranteePlan repayExtraGuaranteePlan) {
        List<RepayPlan> repayPlanList = getRepayPlanRepository().findByLoanIdOrderByPeriod(repayExtraGuaranteePlan.getLoanId());
        List<RepayPlan> list = repayPlanList.stream().filter(repayPlan -> RepayState.NORMAL.equals(repayPlan.getCustRepayState())).toList();

        BigDecimal totalGuaranteeAmt = list.stream().map(repayPlan -> Optional.ofNullable(repayPlan.getConsultFee()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计划结清咨询费
        repayExtraGuaranteePlan.setFeeType(FeeType.CONSULT);
        repayExtraGuaranteePlan.setPlanAmount(totalGuaranteeAmt);
        repayExtraGuaranteePlan.setLeftAmount(totalGuaranteeAmt);

        repayExtraGuaranteePlan.setPlanGuaranteeAmt(BigDecimal.ZERO);
        repayExtraGuaranteePlan.setPlanConsultAmt(totalGuaranteeAmt);
        repayExtraGuaranteePlan.setPlanPlatformPenaltyAmt(BigDecimal.ZERO);

        return getRepayExtraGuaranteePlanRepository().save(repayExtraGuaranteePlan);
    }

    public LoanRepository getLoanRepository() {
        return loanRepository;
    }

    @Autowired
    public void setLoanRepository(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    public RepayPlanRepository getRepayPlanRepository() {
        return repayPlanRepository;
    }

    @Autowired
    public void setRepayPlanRepository(RepayPlanRepository repayPlanRepository) {
        this.repayPlanRepository = repayPlanRepository;
    }

    public RepayExtraGuaranteePlanRepository getRepayExtraGuaranteePlanRepository() {
        return repayExtraGuaranteePlanRepository;
    }

    @Autowired
    public void setRepayExtraGuaranteePlanRepository(RepayExtraGuaranteePlanRepository repayExtraGuaranteePlanRepository) {
        this.repayExtraGuaranteePlanRepository = repayExtraGuaranteePlanRepository;
    }

    public CustomRepayRecordRepository getCustomRepayRecordRepository() {
        return customRepayRecordRepository;
    }

    @Autowired
    public void setCustomRepayRecordRepository(CustomRepayRecordRepository customRepayRecordRepository) {
        this.customRepayRecordRepository = customRepayRecordRepository;
    }
}
