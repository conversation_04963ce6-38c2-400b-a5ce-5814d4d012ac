package com.maguo.loan.cash.flow.service.bound.exchange;

import com.maguo.loan.cash.flow.vo.BaseRequest;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/10/12
 * 换绑确认
 */
public class ExchangeCardConfirmReq extends BaseRequest {

    /**
     * 确认流水号
     */
    @NotBlank(message = "确认流水号不能为空")
    private String confirmFlowNo;

    /**
     * 验证码
     */
    @NotBlank(message = "短信验证码不能为空")
    private String smsCode;

    @NotBlank(message = "借据不能为空")
    private String loanId;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getConfirmFlowNo() {
        return confirmFlowNo;
    }

    public void setConfirmFlowNo(String confirmFlowNo) {
        this.confirmFlowNo = confirmFlowNo;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }
}
