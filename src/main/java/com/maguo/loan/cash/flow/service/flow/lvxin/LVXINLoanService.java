package com.maguo.loan.cash.flow.service.flow.lvxin;


import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.service.flow.AbstractFlowLoanService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;


/**
 * 绿信贷款服务实现类
 * 继承自AbstractFlowLoanService，提供绿信渠道的贷款相关业务处理
 */
@Service
public class LVXINLoanService extends AbstractFlowLoanService {

    /**
     * 获取当前服务对应的流程渠道
     *
     * @return FlowChannel 返回绿信渠道枚举值
     */
    @Override
    public FlowChannel flowChannel() {
        return FlowChannel.LVXIN;
    }

    /**
     * 计算咨询费用
     * 通过IRR36等额本息方式生成还款计划，并计算咨询费
     *
     * @param loan                  贷款实体对象，包含贷款基本信息
     * @param planItemDto           还款计划项DTO，包含当前期数的还款信息
     * @param remainingPrincipalAmt 剩余本金金额
     * @param SAN_LIU_LING          360常量值
     * @param num                   序号参数
     * @return BigDecimal 返回计算后的咨询费用
     */
    @Override
    public BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt, BigDecimal SAN_LIU_LING, long num) {
        //IRR36等额本息月供
        BigDecimal loanMonthRate = RateLevel.RATE_36.getRate().divide(new BigDecimal("12"), 10, RoundingMode.HALF_UP);
        BigDecimal n = loanMonthRate.add(BigDecimal.ONE).pow(loan.getPeriods());
        BigDecimal monthIncome = loan.getAmount().multiply(loanMonthRate).multiply(n).divide(n.subtract(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
        // 计算咨询费
        return monthIncome.subtract(planItemDto.getPrincipalAmt().add(planItemDto.getInterestAmt()).add(planItemDto.getGuaranteeAmt()));
    }
}

