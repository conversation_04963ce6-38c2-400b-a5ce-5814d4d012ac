package com.maguo.loan.cash.flow.entrance.common.util;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class RSAUtil {


    public static final String DEFAULT_ALGORITHM = "RSA";

    private static final String SIGNATURE_ALGORITHM = "SHA1WithRSA";

    public static final String DEFAULT_TRANSFORMATION = "RSA/ECB/PKCS1Padding";

    private static final KeyFactory KEY_FACTORY;

    static {
        try {
            KEY_FACTORY = KeyFactory.getInstance(DEFAULT_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }


    public static PrivateKey loadPrivateKey(String keyStr) throws InvalidKeySpecException {
        return KEY_FACTORY.generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(keyStr)));
    }

    public static PublicKey loadPublicKey(String keyStr) throws InvalidKeySpecException {
        return KEY_FACTORY.generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(keyStr)));
    }


    /**
     * RSA 签名
     */
    public static String signBase64(String content, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();

        return Base64.getEncoder().encodeToString(signed);
    }

    /**
     * 数字签名验证
     */
    public static boolean verifyBase64(String content, String sign, PublicKey publicKey) throws Exception {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(publicKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        return signature.verify(Base64.getDecoder().decode(sign));
    }

    /**
     * 公钥加密
     */
    public static String encryptWithBase64(String data, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance(DEFAULT_TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return Base64.getEncoder().encodeToString(cipher.doFinal(data.getBytes()));
    }

    /**
     * 私钥解密
     */
    public static String decryptWithBase64(String data, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance(DEFAULT_TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return new String(cipher.doFinal(Base64.getDecoder().decode(data)));
    }

    public static KeyPair generateKey(int keySize) throws NoSuchAlgorithmException {
        //初始化密码对生成器（"RSA"，密钥算法标识）
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        //初始化密码对生成器（"1024", 密钥长度, 还支持2048, 4096 长度，第二个参数是个随机数）
        keyPairGen.initialize(keySize, new SecureRandom());
        //使用密码对生成器，产生一对密钥
        return keyPairGen.generateKeyPair();
    }

    private static final int RSA_LENGTH = 2048;

    public static void main(String[] args) {
        try {
            // 生成RSA秘钥对
            KeyPair keyPair = generateKey(RSA_LENGTH);

            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            String publicKeyString = Base64.getEncoder().encodeToString(publicKey.getEncoded());
            String privateKeyString = Base64.getEncoder().encodeToString(privateKey.getEncoded());

            System.out.println("publicKey:\n" + publicKeyString);
            System.out.println("privateKey:\n" + privateKeyString);

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
