package com.maguo.loan.cash.flow.service.event.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestService;
import com.maguo.loan.cash.flow.remote.nfsp.req.SmsSendReq;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.event.SmsSendEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Component
public class SmsSendEventListener implements ApplicationListener<SmsSendEvent> {

    private static final Logger logger = LoggerFactory.getLogger(SmsSendEventListener.class);

    private CommonRequestService commonRequestService;

    private WarningService warningService;

    @Override
    public void onApplicationEvent(SmsSendEvent event) {
        try {
            SmsSendReq smsSendReq = new SmsSendReq();
            smsSendReq.setType(event.getSmsTemplate().getSmsType());
            smsSendReq.setTemplateId(event.getSmsTemplate().getTemplateNo());
            smsSendReq.setMessageParamMap(event.getContentParams());
            smsSendReq.setPhoneList(Lists.newArrayList(event.getReceiverPhone()));
            //
            commonRequestService.request(smsSendReq, new TypeReference<>() {
            });
        } catch (Exception e) {
            warningService.warn("短信发送异常:" + event.getSmsTemplate(), msg -> logger.error(msg, e));
        }
    }

    @Autowired
    public void setCommonRequestService(CommonRequestService commonRequestService) {
        this.commonRequestService = commonRequestService;
    }

    @Autowired
    public void setWarningService(WarningService warningService) {
        this.warningService = warningService;
    }
}
