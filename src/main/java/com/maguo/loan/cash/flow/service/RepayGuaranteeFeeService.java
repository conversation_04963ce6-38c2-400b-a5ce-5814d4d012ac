package com.maguo.loan.cash.flow.service;

import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.dto.repay.OnlineRepayResponseDto;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.OfflineRepayApply;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteeRecord;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayRecordType;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OfflineRepayApplyRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteeRecordRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.service.event.FeeRepayResultEvent;
import com.maguo.loan.cash.flow.util.AmountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Classname RepayGuaranteeFee
 * @Description 融担费还款服务类
 * @Date 2023/10/23 16:31
 * @Created by gale
 */
@Service
public class RepayGuaranteeFeeService {
    private static final Logger logger = LoggerFactory.getLogger(RepayGuaranteeFeeService.class);


    @Autowired
    private RepayExtraGuaranteeRecordRepository repayExtraGuaranteeRecordRepository;


    @Autowired
    private RepayExtraGuaranteePlanRepository repayExtraGuaranteePlanRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private OfflineRepayApplyRepository offlineRepayApplyRepository;


    /**
     * 费用还款
     *
     * @param guaranteePlanId
     */
    public OnlineRepayResponseDto feeRepayOnline(String guaranteePlanId) {
        // 费用还款计划
        RepayExtraGuaranteePlan feePlan = repayExtraGuaranteePlanRepository.findById(guaranteePlanId)
            .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        // 借据
        Loan loan = loanRepository.findById(feePlan.getLoanId())
            .orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
        // 还款卡
        UserBankCard userBankCard = userBankCardRepository.findById(loan.getRepayCardId())
            .orElseThrow(() -> new BizException(ResultCode.CARD_RECORD_NOT_EXIST));
        if (StringUtil.isBlank(userBankCard.getAgreeNo())) {
            throw new BizException(ResultCode.CARD_RECORD_AGREEMENT_NOT_EXIST);
        }

        // 处理中的对客还款记录
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findByLoanIdAndPeriodAndRepayState(
            feePlan.getLoanId(), feePlan.getPeriod(), ProcessState.PROCESSING).orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));

        // 检查,费用还款记录
        List<RepayExtraGuaranteeRecord> feeRepayRecords = repayExtraGuaranteeRecordRepository.findByLoanIdAndPeriodAndFeeTypeAndRepayStateIn(
            feePlan.getLoanId(), feePlan.getPeriod(), feePlan.getFeeType(), List.of(ProcessState.INIT, ProcessState.PROCESSING, ProcessState.SUCCEED));
        if (!CollectionUtils.isEmpty(feeRepayRecords)) {
            throw new BizException(ResultCode.REPAY_CHECK_ERROR);
        }

        //还款记录
        RepayExtraGuaranteeRecord repayFeeRecord = new RepayExtraGuaranteeRecord();
        repayFeeRecord.setRepayPlanId(guaranteePlanId);
        repayFeeRecord.setSourceRepayId(customRepayRecord.getId());
        repayFeeRecord.setLoanId(feePlan.getLoanId());
        repayFeeRecord.setPeriod(feePlan.getPeriod());
        repayFeeRecord.setRepayApplyTime(LocalDateTime.now());
        repayFeeRecord.setRepayPurpose(feePlan.getRepayPurpose());
        repayFeeRecord.setRepayMode(RepayMode.ONLINE);
        repayFeeRecord.setGuaranteeAmt(feePlan.getLeftAmount());
        repayFeeRecord.setRepayState(ProcessState.INIT);
        repayFeeRecord.setAgreementNo(userBankCard.getAgreeNo());
        repayFeeRecord.setFeeType(feePlan.getFeeType());
        RepayExtraGuaranteeRecord record = repayExtraGuaranteeRecordRepository.save(repayFeeRecord);
        // 扣费
        //chargeService.chargeFee(record, customRepayRecord, loan);
        //
        record.setRepayState(ProcessState.PROCESSING);
        repayExtraGuaranteeRecordRepository.save(record);
        logger.info("费用还款记录生成成功:{}", guaranteePlanId);

        OnlineRepayResponseDto response = new OnlineRepayResponseDto();
        response.setCustomRepayRecordId(customRepayRecord.getId());
        response.setRecordId(record.getId());
        response.setRepayRecordType(RepayRecordType.FEE_REPAY_RECORD);
        return response;
    }


    /**
     * 额外费用线下销账
     */
    public String feeRepayOffline(RepayExtraGuaranteePlan plan, OfflineRepayApply repayApply) {
        String guaranteePlanId = plan.getId();

        // 检查,费用还款记录
        List<RepayExtraGuaranteeRecord> feeRepayRecords = repayExtraGuaranteeRecordRepository.findByLoanIdAndPeriodAndFeeTypeAndRepayStateIn(
            plan.getLoanId(), plan.getPeriod(),
            plan.getFeeType(), List.of(ProcessState.INIT, ProcessState.PROCESSING, ProcessState.SUCCEED));
        if (!CollectionUtils.isEmpty(feeRepayRecords)) {
            throw new BizException(ResultCode.REPAY_CHECK_ERROR);
        }

        //对客还款记录
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findTopByLoanIdAndPeriodOrderByCreatedTimeDesc(
            plan.getLoanId(), plan.getPeriod()).orElseThrow();

        //还款记录
        RepayExtraGuaranteeRecord feeRecord = new RepayExtraGuaranteeRecord();
        feeRecord.setRepayPlanId(guaranteePlanId);
        feeRecord.setSourceRepayId(customRepayRecord.getId());
        feeRecord.setLoanId(plan.getLoanId());
        feeRecord.setPeriod(plan.getPeriod());
        feeRecord.setRepayApplyTime(LocalDateTime.now());
        feeRecord.setRepayPurpose(plan.getRepayPurpose());
        feeRecord.setRepayMode(RepayMode.OFFLINE);
        feeRecord.setGuaranteeAmt(AmountUtil.subtract(repayApply.getActAmount(), repayApply.getOverflowAmount()));
        feeRecord.setRepayState(ProcessState.INIT);
        feeRecord.setFeeType(plan.getFeeType());

        feeRecord.setActGuaranteeAmt(repayApply.getActGuaranteeAmt());
        feeRecord.setActConsultAmt(repayApply.getActConsultFee());
        feeRecord.setActPlatformPenaltyAmt(repayApply.getActPenaltyAmt());

        repayExtraGuaranteeRecordRepository.save(feeRecord);

        //更新对客还款记录中减免金额&实还
        customRepayRecord.setReduceAmount(repayApply.getReduceAmount());
        switch (plan.getFeeType()) {
            case GUARANTEE -> {
                customRepayRecord.setGuaranteeAmt(repayApply.getActGuaranteeAmt());
                customRepayRecord.setPenaltyAmt(repayApply.getActPenaltyAmt());
            }
            case CONSULT -> {
                customRepayRecord.setConsultFee(repayApply.getActConsultFee());
                customRepayRecord.setPenaltyAmt(AmountUtil.sum(customRepayRecord.getCapitalPenaltyAmt(), repayApply.getActPenaltyAmt()));
            }
            case GUARANTEE_CONSULT -> {
                customRepayRecord.setGuaranteeAmt(repayApply.getActGuaranteeAmt());
                customRepayRecord.setConsultFee(repayApply.getActConsultFee());
                customRepayRecord.setPenaltyAmt(AmountUtil.sum(customRepayRecord.getCapitalPenaltyAmt(), repayApply.getActPenaltyAmt()));
            }
            default -> {
            }
        }
        //重新计算实还还款总金额
        customRepayRecord.setTotalAmt(AmountUtil.sum(customRepayRecord.getPrincipalAmt(), customRepayRecord.getInterestAmt(),
            customRepayRecord.getGuaranteeAmt(), customRepayRecord.getConsultFee(), customRepayRecord.getPenaltyAmt()));
        customRepayRecord = customRepayRecordRepository.save(customRepayRecord);


        // 线下还服务费
        eventPublisher.publishEvent(new FeeRepayResultEvent(feeRecord.getId(), ProcessState.SUCCEED, null));

        repayApply.setApplyState(ProcessState.SUCCEED);
        offlineRepayApplyRepository.save(repayApply);
        return customRepayRecord.getId();
    }


    /**
     * 费用还款 还款计划的应还金额为零时
     *
     * @param plan              plan
     * @param customRepayRecord customRepayRecord
     */
    public void handleSuccessFeeRepay(RepayExtraGuaranteePlan plan, CustomRepayRecord customRepayRecord) {
        // 更新扣费计划
        plan.setActRepayTime(LocalDateTime.now());
        plan.setPlanState(RepayState.REPAID);
        repayExtraGuaranteePlanRepository.save(plan);
        //还款记录
        RepayExtraGuaranteeRecord repayFeeRecord = new RepayExtraGuaranteeRecord();
        repayFeeRecord.setRepayPlanId(plan.getId());
        repayFeeRecord.setSourceRepayId(customRepayRecord.getId());
        repayFeeRecord.setLoanId(plan.getLoanId());
        repayFeeRecord.setPeriod(plan.getPeriod());
        repayFeeRecord.setRepayApplyTime(LocalDateTime.now());
        repayFeeRecord.setRepayPurpose(plan.getRepayPurpose());
        repayFeeRecord.setRepayMode(RepayMode.ONLINE);
        repayFeeRecord.setGuaranteeAmt(plan.getLeftAmount());
        repayFeeRecord.setActGuaranteeAmt(BigDecimal.ZERO);
        repayFeeRecord.setActConsultAmt(BigDecimal.ZERO);
        repayFeeRecord.setActPlatformPenaltyAmt(BigDecimal.ZERO);
        repayFeeRecord.setRepayState(ProcessState.SUCCEED);
        repayFeeRecord.setFeeType(plan.getFeeType());
        repayFeeRecord.setRemark("应还金额为零");
        repayExtraGuaranteeRecordRepository.save(repayFeeRecord);
    }
}
