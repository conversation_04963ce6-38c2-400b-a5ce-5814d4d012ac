package com.maguo.loan.cash.flow.entrance.common.dto.response.bankCard;

import java.util.List;

/**
 * 获取用户已绑银行卡
 *
 * <AUTHOR>
 * @date 2024/8/23
 */
public class UserBankCardResDTO {

    private List<UserBankCardDTO> bankCardList;

    public static class UserBankCardDTO {
        /**
         * 用户绑卡记录id
         */
        private String bankCardId;
        /**
         * 银行名字
         */
        private String bankName;
        /**
         * 银行code
         */
        private String bankCode;
        /**
         * 身份证号
         */
        private String certNo;
        /**
         * 户名
         */
        private String cardName;
        /**
         * 卡号
         */
        private String cardNo;
        /**
         * 手机号
         */
        private String phone;


        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getCertNo() {
            return certNo;
        }

        public void setCertNo(String certNo) {
            this.certNo = certNo;
        }

        public String getCardName() {
            return cardName;
        }

        public void setCardName(String cardName) {
            this.cardName = cardName;
        }

        public String getCardNo() {
            return cardNo;
        }

        public void setCardNo(String cardNo) {
            this.cardNo = cardNo;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getBankCardId() {
            return bankCardId;
        }

        public void setBankCardId(String bankCardId) {
            this.bankCardId = bankCardId;
        }
    }

    public List<UserBankCardDTO> getBankCardList() {
        return bankCardList;
    }

    public void setBankCardList(List<UserBankCardDTO> bankCardList) {
        this.bankCardList = bankCardList;
    }
}
