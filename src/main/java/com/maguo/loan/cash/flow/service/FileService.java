package com.maguo.loan.cash.flow.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.jinghang.common.util.DateUtil;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

@Service
public class FileService {

    private static final Logger logger = LoggerFactory.getLogger(FileService.class);


    private static final Long EXPIRATION_DAY = 7L;

    private static final int MAX_KEYS = 1000;

    private OSS ossClient;


    private OSS switchOss(String bucketName) {
        return ossClient;
    }

    /**
     * 默认有效期
     *
     * @param bucket
     * @param key
     * @return
     */
    public String getOssUrl(String bucket, String key) {
        return getOssUrl(bucket, key, DateUtil.toDate(LocalDate.now().plusDays(EXPIRATION_DAY)));
    }

    /**
     * 指定有效期
     *
     * @param bucket
     * @param key
     * @param expirationDays
     * @return
     */
    public String getOssUrl(String bucket, String key, Long expirationDays) {
        return getOssUrl(bucket, key, DateUtil.toDate(LocalDate.now().plusDays(expirationDays)));
    }

    public String getOssUrl(String bucket, String key, Date expirationDay) {
        URL url = switchOss(bucket).generatePresignedUrl(bucket, key, expirationDay);
        return url.toString();
    }

    public String getOssUri(String bucket, String key) {
        String url = "";
        try {
            OSSObject ossObject = switchOss(bucket).getObject(bucket, key);
            url = ossObject.getResponse().getUri();
            ossObject.close();
        } catch (IOException e) {
            logger.error("关闭ossObject异常:", e);
        }
        return url;
    }

    /**
     * 获取流需要关闭
     */
    public InputStream getOssFile(String bucket, String key) {
        return switchOss(bucket).getObject(bucket, key).getObjectContent();
    }

    public void getOssFile(String bucket, String key, Consumer<InputStream> streamConsumer) {
        try (OSSObject object = switchOss(bucket).getObject(bucket, key)) {
            streamConsumer.accept(object.getObjectContent());
        } catch (IOException e) {
            logger.error("关闭ossObject异常:", e);
        }
    }

    public void uploadOss(String bucket, String key, InputStream inputStream) {
        switchOss(bucket).putObject(bucket, key, inputStream);
        IOUtils.closeQuietly(inputStream);
    }


    public void delete(String bucket, String key) {
        OSS oss = switchOss(bucket);
        try {
            oss.deleteObject(bucket, key);
        } catch (Exception e) {
            logger.error("OSS文件删除失败", e);
            throw new RuntimeException("OSS文件删除失败");
        } finally {
            if (oss != null) {
                oss.shutdown();
            }
        }
    }

    public void deleteByDirectory(String bucket, String directory) {
        OSS oss = switchOss(bucket);
        try {

            String nextMarker = null;
            ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucket).withPrefix(directory)
                .withMarker(nextMarker).withMaxKeys(MAX_KEYS);
            ObjectListing objectListing;
            List<String> keysToDelete = new ArrayList<>();

            do {
                objectListing = oss.listObjects(listObjectsRequest);
                for (OSSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                    keysToDelete.add(objectSummary.getKey());
                }
                nextMarker = objectListing.getNextMarker();
            } while (objectListing.isTruncated());
            // 批量删除文件
            if (!keysToDelete.isEmpty()) {
                DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucket).withKeys(keysToDelete);
                oss.deleteObjects(deleteObjectsRequest);
                logger.info("oss指定目录 {} 已经删除 ", directory);
            }
        } catch (Exception e) {
            logger.error("OSS文件删除失败", e);
            throw new RuntimeException("OSS文件删除失败");
        } finally {
            if (oss != null) {
                oss.shutdown();
            }
        }
    }


    @Autowired
    public void setOssClient(OSS ossClient) {
        this.ossClient = ossClient;
    }

}
