package com.maguo.loan.cash.flow.entrance.cybk.config;

import com.maguo.loan.cash.flow.common.RequestUriLogFilter;
import com.maguo.loan.cash.flow.entrance.cybk.filter.EncryptFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CompositeFilter;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @Description 长银流量配置
* @Date 2025-07-14
* @Version 1.0
*/
@Configuration
public class CYBKFilterConfig {

    /**
     * 注入配置属性
     * @param cybkConfig
     * @return
     */
    @Bean
    public FilterRegistrationBean<CompositeFilter> cybkFlowFilter(CYBKConfig cybkConfig) {
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new RequestUriLogFilter());
        filterList.add(new EncryptFilter(cybkConfig));
        CompositeFilter compositeFilter = new CompositeFilter();
        compositeFilter.setFilters(filterList);
        FilterRegistrationBean<CompositeFilter> bean = new FilterRegistrationBean<>(compositeFilter);
        // fixme 验证url
        bean.addUrlPatterns("/cybk/*");
        bean.addUrlPatterns("/forward/*");
        return bean;
    }

}
