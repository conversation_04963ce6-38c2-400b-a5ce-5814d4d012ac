package com.maguo.loan.cash.flow.service.event.listener;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderRouterService;
import com.maguo.loan.cash.flow.service.RepayPlanService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.service.event.LoanResultEvent;
import com.maguo.loan.cash.flow.service.event.OrderCancelEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/14
 */
@Component
public class LoanResultEventListener {

    private static final Logger logger = LoggerFactory.getLogger(LoanResultEventListener.class);

    private LoanRepository loanRepository;

    private OrderRepository orderRepository;

    private RepayPlanService repayPlanService;

    private MqService mqService;

    @Autowired
    private AgreementService agreementService;

    @Autowired
    private OrderRouterService orderRouterService;

    @EventListener(LoanResultEvent.class)
    public void onApplicationEvent(LoanResultEvent loanResultEvent) {
        logger.info("处理放款事件:{}", JsonUtil.toJsonString(loanResultEvent));
        Loan loan = loanRepository.findById(loanResultEvent.getLoanId()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
        Order order = orderRepository.findById(loan.getOrderId()).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        ProcessState loanState = loanResultEvent.getLoanState();
        if (ProcessState.SUCCEED.equals(loanState)) {
            // 还款计划
            List<RepayPlan> repayPlans = repayPlanService.generateRepayPlan(loan);
            RepayPlan firstRepayPlan = repayPlans.stream().filter(s -> s.getPeriod() == 1).findFirst().orElseThrow();
            // order订单状态
            order.setOrderState(OrderState.LOAN_PASS);
            order.setLoanTime(loanResultEvent.getLoanTime());
            orderRepository.save(order);
            // 协议签署
            agreementService.applySign(loan.getOrderId(), LoanStage.LOAN, loan.getProjectCode());

        }

        if (ProcessState.FAILED.equals(loanState)) {
            order.setOrderState(OrderState.LOAN_FAIL);
            order.setRemark(loanResultEvent.getFailReason());
            orderRepository.save(order);
        }
        //回调流量
        //取消订单路由
        orderRouterService.cancelOrderRouter(order.getId(), "放款" + (ProcessState.SUCCEED.equals(loanState) ? "成功" : "失败") + ",取消本条路由");
    }


    @EventListener(OrderCancelEvent.class)
    public void onCancelEvent(OrderCancelEvent loanResultEvent) {
        logger.info("处理放款取消/失败事件:{}", JsonUtil.toJsonString(loanResultEvent));

        Order order = orderRepository.findById(loanResultEvent.getOrderId()).orElseThrow(() -> new BizException(ResultCode.ORDER_NOT_EXIST));
        order.setOrderState(OrderState.LOAN_FAIL);
        order.setRemark(loanResultEvent.getRemark());
        orderRepository.save(order);

        Loan loan = loanRepository.findByOrderId(order.getId());
        if (Objects.nonNull(loan)) {
            loan.setLoanState(ProcessState.FAILED);
            loan.setFailReason("二次风控拒绝");
            loanRepository.save(loan);
        }

        CallBackDTO callBackDTO = new CallBackDTO();
        callBackDTO.setBusinessId(order.getId());
        callBackDTO.setFlowChannel(order.getFlowChannel());
        callBackDTO.setCallbackState(CallbackState.LOAN_FAIL);
        callBackDTO.setRemark(loanResultEvent.getRemark());
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
    }


    private void repayPlanSyncProcess(Loan loan) {
        logger.info("放款成功后 向fin-core同步还款计划 loanId:{}", loan.getId());
        // 同步资方还款计划
        mqService.submitRepayPlanSync(loan.getId());
    }


    @Autowired
    public void setLoanRepository(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    @Autowired
    public void setOrderRepository(OrderRepository orderRepository) {
        this.orderRepository = orderRepository;
    }

    @Autowired
    public void setRepayPlanService(RepayPlanService repayPlanService) {
        this.repayPlanService = repayPlanService;
    }

    @Autowired
    public void setMqService(MqService mqService) {
        this.mqService = mqService;
    }


}
