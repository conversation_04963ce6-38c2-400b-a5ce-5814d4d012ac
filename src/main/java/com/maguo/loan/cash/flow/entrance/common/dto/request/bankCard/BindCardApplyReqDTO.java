package com.maguo.loan.cash.flow.entrance.common.dto.request.bankCard;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/8/23
 */
public class BindCardApplyReqDTO {
    /**
     * 卡号
     */
    @NotBlank
    private String cardNo;
    /**
     * 手机号
     */
    @NotBlank
    private String phone;
    /**
     * 合作方单号
     */
    @NotBlank
    private String partnerOrderNo;

    /**
     * 银行代码
     */
    @NotBlank
    private String bankCode;

    /**
     * 户名
     */
    @NotBlank
    private String cardName;
    /**
     * 身份证号
     */
    @NotBlank
    private String certNo;

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }
}
