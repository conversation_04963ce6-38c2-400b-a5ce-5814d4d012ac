package com.maguo.loan.cash.flow.service.flow.ppcjdl;


import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.service.flow.AbstractFlowLoanService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * PPCJDL贷款服务实现类
 * 提供PPCJDL渠道的贷款相关业务处理功能
 */
@Service
public class PPCJDLLoanService extends AbstractFlowLoanService {

    /**
     * 获取当前服务对应的流程渠道
     *
     * @return FlowChannel 返回PPCJDL渠道枚举值
     */
    @Override
    public FlowChannel flowChannel() {
        return FlowChannel.PPCJDL;
    }

    /**
     * 计算咨询费用
     * 根据剩余本金金额和固定月费率计算咨询费用，结果保留2位小数并向下舍入
     *
     * @param loan                  贷款实体对象
     * @param planItemDto           还款计划项DTO
     * @param remainingPrincipalAmt 剩余本金金额
     * @param SAN_LIU_LING          360常量值（未使用）
     * @param num                   数量参数（未使用）
     * @return BigDecimal 返回计算后的咨询费用
     */
    @Override
    public BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt, BigDecimal SAN_LIU_LING, long num) {
        // 使用RateLevel.CONSULT_FEE_MONTH_RATE_36的费率计算咨询费用，并保留2位小数向下舍入
        return remainingPrincipalAmt.multiply(RateLevel.CONSULT_FEE_MONTH_RATE_36.getRate()).setScale(2, RoundingMode.HALF_DOWN);
    }
}

