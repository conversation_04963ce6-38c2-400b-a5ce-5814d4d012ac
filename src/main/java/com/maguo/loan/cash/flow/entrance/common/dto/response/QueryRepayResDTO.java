package com.maguo.loan.cash.flow.entrance.common.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.entrance.common.enums.RepayResult;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 查询还款结果
 *
 * <AUTHOR>
 * @date 2024/8/27
 */
public class QueryRepayResDTO {

    /**
     * 还款流水标识
     */
    private String repayId;

    /**
     * 期次
     */
    private Integer period;
    /**
     * 合作机构单号
     */
    private String partnerOrderNo;

    /**
     * 合作机构流水号
     */
    private String partnerRepayNo;
    /**
     * 还款状态
     */
    private String repayStatus;

    /**
     * 还款结果
     */
    private RepayResult repayState;

    /**
     * 实还款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime actRepayTime;
    /**
     * 实还款本金
     */
    private BigDecimal actPrincipalAmt = BigDecimal.ZERO;
    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt = BigDecimal.ZERO;
    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt = BigDecimal.ZERO;
    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeAmt = BigDecimal.ZERO;
    /**
     * 实还咨询费
     */
    private BigDecimal actConsultFee = BigDecimal.ZERO;

    /**
     * 实还总额
     */
    private BigDecimal actAmount = BigDecimal.ZERO;

    /**
     * 剩余本金
     */
    private BigDecimal remainPrincipalAmt;
    /**
     * 剩余利息
     */
    private BigDecimal remainInterestAmt;
    /**
     * 剩余罚息
     */
    private BigDecimal remainPenaltyAmt;
    /**
     * 剩余担保费
     */
    private BigDecimal remainGuaranteeAmt;
    /**
     * 剩余咨询费
     */
    private BigDecimal remainConsultFee;
    /**
     * 剩余违约金
     */
    private BigDecimal remainBreachAmt = BigDecimal.ZERO;
    /**
     * 剩余待还总额
     */
    private BigDecimal remainAmount;
    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getPartnerRepayNo() {
        return partnerRepayNo;
    }

    public void setPartnerRepayNo(String partnerRepayNo) {
        this.partnerRepayNo = partnerRepayNo;
    }

    public String getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(String repayStatus) {
        this.repayStatus = repayStatus;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActGuaranteeAmt() {
        return actGuaranteeAmt;
    }

    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }

    public BigDecimal getActConsultFee() {
        return actConsultFee;
    }

    public void setActConsultFee(BigDecimal actConsultFee) {
        this.actConsultFee = actConsultFee;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public BigDecimal getRemainPrincipalAmt() {
        return remainPrincipalAmt;
    }

    public void setRemainPrincipalAmt(BigDecimal remainPrincipalAmt) {
        this.remainPrincipalAmt = remainPrincipalAmt;
    }

    public BigDecimal getRemainInterestAmt() {
        return remainInterestAmt;
    }

    public void setRemainInterestAmt(BigDecimal remainInterestAmt) {
        this.remainInterestAmt = remainInterestAmt;
    }

    public BigDecimal getRemainPenaltyAmt() {
        return remainPenaltyAmt;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public void setRemainPenaltyAmt(BigDecimal remainPenaltyAmt) {
        this.remainPenaltyAmt = remainPenaltyAmt;
    }

    public BigDecimal getRemainGuaranteeAmt() {
        return remainGuaranteeAmt;
    }

    public void setRemainGuaranteeAmt(BigDecimal remainGuaranteeAmt) {
        this.remainGuaranteeAmt = remainGuaranteeAmt;
    }

    public BigDecimal getRemainConsultFee() {
        return remainConsultFee;
    }

    public void setRemainConsultFee(BigDecimal remainConsultFee) {
        this.remainConsultFee = remainConsultFee;
    }

    public BigDecimal getRemainBreachAmt() {
        return remainBreachAmt;
    }

    public void setRemainBreachAmt(BigDecimal remainBreachAmt) {
        this.remainBreachAmt = remainBreachAmt;
    }

    public BigDecimal getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(BigDecimal remainAmount) {
        this.remainAmount = remainAmount;
    }

    public RepayResult getRepayState() {
        return repayState;
    }

    public void setRepayState(RepayResult repayState) {
        this.repayState = repayState;
    }

    public String getRepayId() {
        return repayId;
    }

    public void setRepayId(String repayId) {
        this.repayId = repayId;
    }
}
