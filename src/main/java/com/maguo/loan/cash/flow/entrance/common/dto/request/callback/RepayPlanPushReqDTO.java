package com.maguo.loan.cash.flow.entrance.common.dto.request.callback;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.entrance.common.dto.request.base.CommonApiRequest;
import com.maguo.loan.cash.flow.entrance.common.enums.api.CallBackApi;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 还款计划推送
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
public class RepayPlanPushReqDTO implements CommonApiRequest {
    /**
     * 我方单号
     */
    private String orderId;
    /**
     * 合作机构单号
     */
    private String partnerOrderNo;
    /**
     * 还款计划
     */
    private List<RepayPlanDTO> repayPlanList;

    public static class RepayPlanDTO {

        /**
         * 期次
         */
        private Integer period;
        /**
         * 计划还款日
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate planRepayDate;
        /**
         * 应还总额
         */
        private BigDecimal amount;
        /**
         * 应还本金
         */
        private BigDecimal principalAmt;
        /**
         * 应还利息
         */
        private BigDecimal interestAmt;
        /**
         * 应还罚息
         */
        private BigDecimal penaltyAmt;
        /**
         * 应还担保费
         */
        private BigDecimal guaranteeAmt;
        /**
         * 应还咨询费
         */
        private BigDecimal consultFee;
        /**
         * 还款状态
         */
        private String custRepayState;
        /**
         * 实还款时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime actRepayTime;
        /**
         * 实还款本金
         */
        private BigDecimal actPrincipalAmt;
        /**
         * 实还利息
         */
        private BigDecimal actInterestAmt;
        /**
         * 实还罚息
         */
        private BigDecimal actPenaltyAmt;
        /**
         * 实还担保费
         */
        private BigDecimal actGuaranteeAmt;
        /**
         * 实还咨询费
         */
        private BigDecimal actConsultFee;
        /**
         * 实还总额
         */
        private BigDecimal actAmount;
        /**
         * 剩余本金
         */
        private BigDecimal remainPrincipalAmt;
        /**
         * 剩余利息
         */
        private BigDecimal remainInterestAmt;
        /**
         * 剩余罚息
         */
        private BigDecimal remainPenaltyAmt;
        /**
         * 剩余担保费
         */
        private BigDecimal remainGuaranteeAmt;
        /**
         * 剩余咨询费
         */
        private BigDecimal remainConsultFee;

        /**
         * 剩余待还总额
         */
        private BigDecimal remainAmount;

        /**
         * 减免金额
         */
        private BigDecimal reduceAmount;

        public BigDecimal getReduceAmount() {
            return reduceAmount;
        }

        public void setReduceAmount(BigDecimal reduceAmount) {
            this.reduceAmount = reduceAmount;
        }

        public Integer getPeriod() {
            return period;
        }

        public void setPeriod(Integer period) {
            this.period = period;
        }

        public LocalDate getPlanRepayDate() {
            return planRepayDate;
        }

        public void setPlanRepayDate(LocalDate planRepayDate) {
            this.planRepayDate = planRepayDate;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public BigDecimal getPrincipalAmt() {
            return principalAmt;
        }

        public void setPrincipalAmt(BigDecimal principalAmt) {
            this.principalAmt = principalAmt;
        }

        public BigDecimal getInterestAmt() {
            return interestAmt;
        }

        public void setInterestAmt(BigDecimal interestAmt) {
            this.interestAmt = interestAmt;
        }

        public BigDecimal getPenaltyAmt() {
            return penaltyAmt;
        }

        public void setPenaltyAmt(BigDecimal penaltyAmt) {
            this.penaltyAmt = penaltyAmt;
        }

        public BigDecimal getGuaranteeAmt() {
            return guaranteeAmt;
        }

        public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
            this.guaranteeAmt = guaranteeAmt;
        }

        public BigDecimal getConsultFee() {
            return consultFee;
        }

        public void setConsultFee(BigDecimal consultFee) {
            this.consultFee = consultFee;
        }

        public String getCustRepayState() {
            return custRepayState;
        }

        public void setCustRepayState(String custRepayState) {
            this.custRepayState = custRepayState;
        }

        public LocalDateTime getActRepayTime() {
            return actRepayTime;
        }

        public void setActRepayTime(LocalDateTime actRepayTime) {
            this.actRepayTime = actRepayTime;
        }

        public BigDecimal getActPrincipalAmt() {
            return actPrincipalAmt;
        }

        public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
            this.actPrincipalAmt = actPrincipalAmt;
        }

        public BigDecimal getActInterestAmt() {
            return actInterestAmt;
        }

        public void setActInterestAmt(BigDecimal actInterestAmt) {
            this.actInterestAmt = actInterestAmt;
        }

        public BigDecimal getActPenaltyAmt() {
            return actPenaltyAmt;
        }

        public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
            this.actPenaltyAmt = actPenaltyAmt;
        }

        public BigDecimal getActGuaranteeAmt() {
            return actGuaranteeAmt;
        }

        public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
            this.actGuaranteeAmt = actGuaranteeAmt;
        }

        public BigDecimal getActConsultFee() {
            return actConsultFee;
        }

        public void setActConsultFee(BigDecimal actConsultFee) {
            this.actConsultFee = actConsultFee;
        }

        public BigDecimal getActAmount() {
            return actAmount;
        }

        public void setActAmount(BigDecimal actAmount) {
            this.actAmount = actAmount;
        }

        public BigDecimal getRemainPrincipalAmt() {
            return remainPrincipalAmt;
        }

        public void setRemainPrincipalAmt(BigDecimal remainPrincipalAmt) {
            this.remainPrincipalAmt = remainPrincipalAmt;
        }

        public BigDecimal getRemainInterestAmt() {
            return remainInterestAmt;
        }

        public void setRemainInterestAmt(BigDecimal remainInterestAmt) {
            this.remainInterestAmt = remainInterestAmt;
        }

        public BigDecimal getRemainPenaltyAmt() {
            return remainPenaltyAmt;
        }

        public void setRemainPenaltyAmt(BigDecimal remainPenaltyAmt) {
            this.remainPenaltyAmt = remainPenaltyAmt;
        }

        public BigDecimal getRemainGuaranteeAmt() {
            return remainGuaranteeAmt;
        }

        public void setRemainGuaranteeAmt(BigDecimal remainGuaranteeAmt) {
            this.remainGuaranteeAmt = remainGuaranteeAmt;
        }

        public BigDecimal getRemainConsultFee() {
            return remainConsultFee;
        }

        public void setRemainConsultFee(BigDecimal remainConsultFee) {
            this.remainConsultFee = remainConsultFee;
        }

        public BigDecimal getRemainAmount() {
            return remainAmount;
        }

        public void setRemainAmount(BigDecimal remainAmount) {
            this.remainAmount = remainAmount;
        }
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public List<RepayPlanDTO> getRepayPlanList() {
        return repayPlanList;
    }

    public void setRepayPlanList(List<RepayPlanDTO> repayPlanList) {
        this.repayPlanList = repayPlanList;
    }

    @Override
    public CallBackApi getApiType() {
        return CallBackApi.REPAY_PLAN_PUSH;
    }
}
