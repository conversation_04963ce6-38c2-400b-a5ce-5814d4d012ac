package com.maguo.loan.cash.flow.entrance.common.strategy.impl;

import com.maguo.loan.cash.flow.entrance.common.strategy.ProductCodeStrategy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 扩展预留
 */
@Component
public class CapitalSourceStrategy implements ProductCodeStrategy {

    @Override
    public boolean supports(String source) {
        return "CAPITAL".equalsIgnoreCase(source);
    }

    @Override
    public String buildProductCode(Map<String, String> params) {
        String capitalChannel = params.get("capitalChannel");
        String capitalProductType = params.get("capitalProductType");
        return capitalChannel + "_" + capitalProductType;
    }
}
