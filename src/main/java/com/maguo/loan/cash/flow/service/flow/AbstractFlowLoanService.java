package com.maguo.loan.cash.flow.service.flow;

import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.maguo.loan.cash.flow.entity.Loan;

import java.math.BigDecimal;


/**
 * 抽象贷款流程服务类，提供贷款现金流计算的基础框架
 * 实现IFlowService接口，定义贷款咨询费计算的抽象方法
 */
public abstract class AbstractFlowLoanService implements IFlowService {
    /**
     * 计算贷款计划咨询费的抽象方法
     *
     * @param loan 贷款实体对象，包含贷款基本信息
     * @param planItemDto 还款计划项DTO，包含还款计划相关数据
     * @param remainingPrincipalAmt 剩余本金金额
     * @param SAN_LIU_LING 常量值360，用于计算
     * @param num 计算相关的数值参数
     * @return BigDecimal 返回计算后的咨询费用
     */
    public abstract BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt, BigDecimal SAN_LIU_LING, long num);
}

