package com.maguo.loan.cash.flow.service;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.BaoFuPayMerchantConfigManager;
import com.zsjz.third.part.baofoo.settlement.BaofooMerchantConfig;
import com.maguo.loan.cash.flow.convert.RepayConvert;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.WithholdAccountConfig;
import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.entity.WithholdShareInfo;
import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.Payee;
import com.maguo.loan.cash.flow.enums.PaymentChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.WithholdAccountConfigRepository;
import com.maguo.loan.cash.flow.repository.WithholdFlowRepository;
import com.maguo.loan.cash.flow.repository.WithholdShareInfoRepository;
import com.maguo.loan.cash.flow.service.pay.ThirdPayService;
import com.maguo.loan.cash.flow.util.AmountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/6 11:13
 * 扣款
 */
@Service
public class ChargeService {

    private static final Logger logger = LoggerFactory.getLogger(ChargeService.class);

    @Autowired
    private WithholdFlowRepository withholdFlowRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private WithholdShareInfoRepository withholdShareInfoRepository;

    @Autowired
    private ThirdPayService thirdPayService;

    @Autowired
    private PlatformOnceBoundService platformOnceBoundService;

    @Autowired
    private WithholdAccountConfigRepository withholdAccountConfigRepository;

    @Autowired
    private BaoFuPayMerchantConfigManager baofooMerchantConfigManager;


    private List<WithholdShareInfo> getWithholdShareInfo(WithholdFlow withholdFlow, CustomRepayRecord record, WithholdAccountConfig accountConfig) {
        List<WithholdShareInfo> list = new ArrayList<>(2);
        // 长银
        WithholdShareInfo sonWithholdShareInfo = new WithholdShareInfo();
        sonWithholdShareInfo.setPrincipal(record.getPrincipalAmt());
        sonWithholdShareInfo.setInterest(record.getInterestAmt());
        sonWithholdShareInfo.setBreach(BigDecimal.ZERO);
        sonWithholdShareInfo.setPenalty(record.getCapitalPenaltyAmt());
        sonWithholdShareInfo.setGuarantee(BigDecimal.ZERO);
        sonWithholdShareInfo.setConsult(BigDecimal.ZERO);
        sonWithholdShareInfo.setWithholdId(withholdFlow.getId());
        //本息分账商户号
        sonWithholdShareInfo.setMerchantNo(accountConfig.getPrincipalShareMemberId());
        sonWithholdShareInfo.setMerchantConfirm(WhetherState.Y);
        sonWithholdShareInfo.setAmount(AmountUtil.sum(record.getPrincipalAmt(), record.getInterestAmt(), record.getCapitalPenaltyAmt()));
        list.add(sonWithholdShareInfo);
        // 担保商户号  = 信合元商户号
        WithholdShareInfo motherWithholdShareInfo = new WithholdShareInfo();
        motherWithholdShareInfo.setPrincipal(BigDecimal.ZERO);
        motherWithholdShareInfo.setInterest(BigDecimal.ZERO);
        motherWithholdShareInfo.setBreach(record.getBreachAmt());
        motherWithholdShareInfo.setPenalty(record.getPenaltyAmt().subtract(record.getCapitalPenaltyAmt()));
        motherWithholdShareInfo.setGuarantee((BigDecimal.ZERO));
        motherWithholdShareInfo.setConsult(record.getConsultFee());
        motherWithholdShareInfo.setWithholdId(withholdFlow.getId());
        motherWithholdShareInfo.setMerchantNo(accountConfig.getGuaranteeShareMemberId());
        motherWithholdShareInfo.setMerchantConfirm(WhetherState.N);
        //母单金额=咨询费+违约金+（罚息-资方罚息）
        motherWithholdShareInfo.setAmount(record.getConsultFee().add(record.getBreachAmt().add(record.getPenaltyAmt().subtract(record.getCapitalPenaltyAmt()))));

        list.add(motherWithholdShareInfo);
        return list;
    }


    public void chargeMotherAndChild(CustomRepayRecord record, Loan loan) {
        logger.info("代偿前,母子订单逻辑,对客还款记录id:{},借据id:{}", record.getId(), loan.getId());
        // 获取当期还款计划
        RepayPlan currentRepayPlan = repayPlanRepository.findByLoanIdAndPeriod(loan.getId(), record.getPeriod());

        if (Objects.equals(currentRepayPlan.getCustRepayState(), RepayState.REPAID)) {
            // 当前已还还款失败
            updateFailRepayRecord(record, "当期已还！");
            throw new BizException("当期已还", ResultCode.BIZ_ERROR);
        }

        if (RepayPurpose.CLEAR.equals(record.getRepayPurpose()) && LocalDate.now().isAfter(currentRepayPlan.getPlanRepayDate())) {
            // 逾期不可线上提前结清
            updateFailRepayRecord(record, "当前已逾期, 不可线上提前结清");
            throw new BizException("当前已逾期, 不可线上提前结清", ResultCode.BIZ_ERROR);
        }

        //扣款商户号配置
        WithholdAccountConfig accountConfig = withholdAccountConfigRepository.findByBankChannelAndFlowChannelAndGuaranteeCompany(loan.getBankChannel(),
            loan.getFlowChannel(), loan.getGuaranteeCompany());

        // 扣款记录
        WithholdFlow withholdFlow = RepayConvert.INSTANCE.toWithholdFlow(record);
        withholdFlow.setBizType(ChargeBizType.FINANCE);
        withholdFlow.setPayState(ProcessState.PROCESSING);
        //扣款商户号
        BaofooMerchantConfig merchantConfig = baofooMerchantConfigManager.getMerchantConfig(accountConfig.getMainMemberId());
        withholdFlow.setCommonWithholdType(accountConfig.getMainMemberType());
        withholdFlow.setWithholdTypeMemberId(accountConfig.getMainMemberId());
        withholdFlow.setChannelMchId(merchantConfig.getTerminalId());
        if (RepayPurpose.CLEAR.equals(record.getRepayPurpose())) {
            // (结清扣本息 子单)+(咨询费+违约金 母单)
            withholdFlow.setPayAmount(AmountUtil.sum(record.getPrincipalAmt(), record.getInterestAmt(), record.getConsultFee(), record.getBreachAmt()));
        } else {
            // 当期(扣本+息+资方罚息 子单)+(咨询费+对客罚息-对资罚息 母单)
            BigDecimal totalAmt = AmountUtil.sum(record.getPrincipalAmt(), record.getInterestAmt(), record.getCapitalPenaltyAmt(),
                record.getConsultFee(),
                (record.getPenaltyAmt().subtract(record.getCapitalPenaltyAmt())));
            //totalAmt.subtract(record.getReduceAmount());
            withholdFlow.setPayAmount(totalAmt);
        }
        // 绑卡记录
        UserBankCard userBankCard = platformOnceBoundService.obtainRepayCard(loan);
        if (Objects.isNull(userBankCard)) {
            throw new BizException(ResultCode.USER_BANK_CARD_FAIL);
        }
        withholdFlow.setPayee(Payee.CAPITAL);
        withholdFlow.setChannelId(PaymentChannel.BAOFU);

        withholdFlow.setShareInfo(WhetherState.Y);
        withholdFlow.setAgreementNo(userBankCard.getAgreeNo());
        withholdFlow = withholdFlowRepository.saveAndFlush(withholdFlow);
        logger.info("代偿前,母子订单逻辑,初始化扣款记录,id:{}", withholdFlow.getId());
        List<WithholdShareInfo> list = getWithholdShareInfo(withholdFlow, record, accountConfig);
        logger.info("代偿前,母子订单逻辑,需要分账的账户:{}", JsonUtil.toJsonString(list));
        withholdShareInfoRepository.saveAllAndFlush(list);
        // 发起代扣
        thirdPayService.apply(withholdFlow);
    }


    public void updateFailRepayRecord(CustomRepayRecord record, String failReason) {
        record.setFailReason(failReason);
        record.setRepayState(ProcessState.FAILED);
        customRepayRecordRepository.save(record);
    }
}
