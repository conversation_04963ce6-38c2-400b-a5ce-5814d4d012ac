package com.maguo.loan.cash.flow.entrance.common.dto.request.callback;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.entrance.common.dto.request.base.CommonApiRequest;
import com.maguo.loan.cash.flow.entrance.common.enums.api.CallBackApi;


import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单状态推送
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
public class OrderStatusPushReqDTO implements CommonApiRequest {
    /**
     * 合作机构单号
     */
    private String partnerOrderNo;
    /**
     * 我方单号
     */
    private String orderId;

    private String bankChannelName;
    /**
     * 订单状态
     */
    private String orderState;
    /**
     * 放款金额
     */
    private BigDecimal loanAmount;
    /**
     * 放款成功时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loanSuccessTime;
    /**
     * 失败原因
     */
    private String rejectReason;
    /**
     * 失败后下次可以要款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime freezeDay;

    /**
     * 复借标签
     * Y- 是
     * N - 不是
     */
    private String reLoan;

    public String getReLoan() {
        return reLoan;
    }

    public void setReLoan(String reLoan) {
        this.reLoan = reLoan;
    }

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public LocalDateTime getLoanSuccessTime() {
        return loanSuccessTime;
    }

    public void setLoanSuccessTime(LocalDateTime loanSuccessTime) {
        this.loanSuccessTime = loanSuccessTime;
    }

    public LocalDateTime getFreezeDay() {
        return freezeDay;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setFreezeDay(LocalDateTime freezeDay) {
        this.freezeDay = freezeDay;
    }

    public String getBankChannelName() {
        return bankChannelName;
    }

    public void setBankChannelName(String bankChannelName) {
        this.bankChannelName = bankChannelName;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    @Override
    public CallBackApi getApiType() {
        return CallBackApi.ORDER_STATUS_PUSH;
    }
}
