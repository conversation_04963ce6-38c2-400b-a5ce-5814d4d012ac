package com.maguo.loan.cash.flow.service.bound;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jinghang.common.http.exception.HttpException;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.convert.CardConvert;
import com.maguo.loan.cash.flow.entity.BankList;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.remote.cardbin.impl.AlipayCardBinService;
import com.maguo.loan.cash.flow.remote.cardbin.impl.LocalCardBinService;
import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestService;
import com.maguo.loan.cash.flow.remote.nfsp.NfspBizException;
import com.maguo.loan.cash.flow.remote.nfsp.req.AgreementPayQueryBindingReq;
import com.maguo.loan.cash.flow.remote.nfsp.req.BindApplyReq;
import com.maguo.loan.cash.flow.remote.nfsp.req.BindConfirmReq;
import com.maguo.loan.cash.flow.remote.nfsp.rsp.BindApplyRsp;
import com.maguo.loan.cash.flow.remote.nfsp.rsp.BindConfirmRsp;
import com.maguo.loan.cash.flow.repository.BankListRepository;
import com.maguo.loan.cash.flow.repository.BindCardRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 银行卡服务
 */
@Service
public class PlatformCardService {

    private static final Logger logger = LoggerFactory.getLogger(PlatformCardService.class);

    private static final String CCB_ABBR = "CCB";


    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private BankListRepository bankListRepository;


    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private BindCardRecordRepository bindCardRecordRepository;

    @Autowired
    private CommonRequestService commonRequestService;

    @Autowired
    private LocalCardBinService localCardBinService;

    @Autowired
    private AlipayCardBinService alipayCardBinService;



    @Autowired
    private OrderRepository orderRepository;



    /**
     * 查询银行列表
     *
     * @return 银行列表
     */
    public List<BankList> findPlatformAvailiableBankList() {
        return bankListRepository.findBankListByStatus(AbleStatus.ENABLE);
    }

    /**
     * 查询可用银行列表
     * 如果订单为权益订单，则去除建设银行（CCB）
     *
     * @param order
     * @return
     */
    public List<BankList> findRightsOrderBankListWithoutCcb(Order order) {
        List<BankList> bankList = findPlatformAvailiableBankList();
        if (Objects.nonNull(order) && RightsLevel.NONE != order.getApproveRights()) {
            //建设银行不支持扣权益，如果为权益订单，则移除建设银行CCB
            bankList = bankList.stream().filter(s -> !StringUtils.equals(CCB_ABBR, s.getAbbr())).collect(Collectors.toList());
        }
        return bankList;
    }


    public String findBankNameByAbbr(String abbr) {
        return bankListRepository.findByAbbr(abbr).map(BankList::getShortName).orElse("");
    }


    private boolean isValidCard(final String cardBin, Order order) {
        List<BankList> bankList = findRightsOrderBankListWithoutCcb(order);
        return bankList.stream().anyMatch(b -> b.getAbbr().equalsIgnoreCase(cardBin));
    }





    /**
     * 绑卡申请
     *
     * @param certNo 身份证号
     * @param cardNo 银行卡号
     * @param phone  手机
     * @param name   姓名
     */
    public BindCardRecord bindApply(String certNo, String cardNo, String phone, String name, String userId, Order order, ProtocolChannel protocolChannel) {
        UserInfo userInfo;

        if (userId == null) {
            userInfo = userInfoRepository.findByCertNo(certNo);
        } else {
            userInfo = userInfoRepository.findById(userId).orElse(null);
        }

        if (userInfo == null) {
            throw new BizException(ResultCode.USER_NOT_EXIST);
        }
        cardNo="6220000";
        CardBin cardBin = queryCardBin(cardNo);
        if (cardBin == null) {
            throw new BizException(ResultCode.CARD_NOT_SUPPORT);
        }

        BindCardRecord bindRecord = new BindCardRecord();
        bindRecord.setUserId(userInfo.getId());
        bindRecord.setBankCardNo(cardNo);
        bindRecord.setCertNo(certNo);
        bindRecord.setPhone(phone);
        bindRecord.setName(name);
        bindRecord.setBankCode(cardBin.getBankAbbr());
        bindRecord.setBankName(cardBin.getShortName());
        bindRecord.setBoundSide(BoundSide.PLATFORM);
        // 设置绑卡商户号.
        bindRecord.setChannel(protocolChannel);
        boolean validCard = isValidCard(cardBin.getBankAbbr(), order);
        if (!validCard) {
            bindRecord.setState(ProcessState.FAILED);
            bindRecord.setFailReason("银行卡不在平台支持中");
            return bindCardRecordRepository.save(bindRecord);
        }

        bindRecord.setState(ProcessState.INIT);
        bindRecord = bindCardRecordRepository.save(bindRecord);
        BindCardRecord finalRecord = commonBindApply(bindRecord);
        return bindCardRecordRepository.save(finalRecord);
    }

    /**
     * 换绑卡申请
     *
     * @param loan
     * @param exchangeCardApplyReq
     * @return
     */
    public BindCardRecord bindApplyLoan(Loan loan, ExchangeCardApplyReq exchangeCardApplyReq) {
        UserInfo userInfo = userInfoRepository.findById(loan.getUserId()).orElseThrow(() -> new BizException(ResultCode.USER_NOT_EXIST));
        Order order = orderRepository.findOrderById(loan.getOrderId());
        CardBin cardBin = queryCardBin(exchangeCardApplyReq.getCardNo());
        if (Objects.isNull(cardBin)) {
            throw new BizException(ResultCode.CARD_NOT_SUPPORT);
        }

        BindCardRecord bindRecord = new BindCardRecord();
        bindRecord.setUserId(userInfo.getId());
        bindRecord.setBankCardNo(exchangeCardApplyReq.getCardNo());
        bindRecord.setCertNo(userInfo.getCertNo());
        bindRecord.setPhone(exchangeCardApplyReq.getPhone());
        bindRecord.setName(userInfo.getName());
        bindRecord.setBankCode(cardBin.getBankAbbr());
        // 设置绑卡商户号
        bindRecord.setChannel(ProtocolChannel.BF);
        boolean validCard = isValidCard(cardBin.getBankAbbr(), order);
        if (!validCard) {
            bindRecord.setState(ProcessState.FAILED);
            bindRecord.setFailReason("银行卡不在平台支持中");
            return bindCardRecordRepository.save(bindRecord);
        }
        bindRecord.setState(ProcessState.INIT);
        bindRecord.setBoundSide(exchangeCardApplyReq.getBoundSide());
        bindRecord.setBankName(cardBin.getShortName());
        bindRecord = bindCardRecordRepository.save(bindRecord);
        BindCardRecord finalRecord = commonBindApply(bindRecord);
        if (ProcessState.FAILED.equals(finalRecord.getState())) {
            throw new BizException(finalRecord.getFailReason(), ResultCode.CARD_APPLY_FAIL);
        }
        return bindCardRecordRepository.save(finalRecord);

    }

    /**
     * 绑卡申请确认
     *
     * @param validCode 验证码
     * @param recordId  绑卡记录id
     */
    public BindCardRecord bindConfirm(String validCode, String recordId) {

        BindCardRecord record = bindCardRecordRepository.findById(recordId)
                .orElseThrow(() -> new BizException(ResultCode.CARD_RECORD_NOT_EXIST));

        ProcessState state = record.getState();
        //已绑卡成功
        if (ProcessState.SUCCEED == state) {
            return record;
        }

        BindCardRecord confirm = commonBindConfirm(record, validCode);
        BindCardRecord finalRecord = bindCardRecordRepository.save(confirm);
        if (finalRecord.getState() == ProcessState.SUCCEED) {
            saveSuccessRecord(finalRecord);
        }
        return finalRecord;
    }

    /**
     * 绑卡申请
     *
     * @param record 绑卡记录
     * @return 记录
     */
    private BindCardRecord commonBindApply(BindCardRecord record) {
        return switch (record.getChannel()) {
            case BF -> bindApplyBf(record);
            case ALLIN_PAY -> bindConfirmAllinPay(record, null);

        };
    }


    /**
     * 绑卡申请
     *
     * @param record 绑卡记录
     * @return 记录
     */
    private BindCardRecord commonBindConfirm(BindCardRecord record, String verifyCode) {
        return switch (record.getChannel()) {
            case BF -> bindConfirmBf(record, verifyCode);
            case ALLIN_PAY -> bindConfirmAllinPay(record, verifyCode);

        };
    }

    private BindCardRecord bindConfirmAllinPay(BindCardRecord record, String verifyCode) {
        logger.warn("不支持AllinPay绑卡。");
        record.setState(ProcessState.FAILED);
        record.setFailReason("不支持绑卡 ");
        return record;
    }

    /**
     * 绑卡申请-宝付
     *
     * @param record
     * @return
     */
    private BindCardRecord bindApplyBf(BindCardRecord record) {
        BindApplyReq bindApplyReq = CardConvert.INSTANCE.toCommonBindApply(record);
        try {
            BindApplyRsp rsp = commonRequestService.request(bindApplyReq, new TypeReference<BindApplyRsp>() {
            });
            record.setConfirmOrderNo(rsp.getOrderNo());
            record.setState(ProcessState.PROCESSING);
        } catch (HttpException | JsonProcessingException | NfspBizException e) {
            logger.error("请求公共异常", e);
            record.setState(ProcessState.FAILED);
            record.setFailReason("请求公共异常: " + e.getMessage());
        }
        return record;
    }

    /**
     * 绑卡确认-宝付
     *
     * @param record
     * @param verifyCode
     * @return
     */
    private BindCardRecord bindConfirmBf(BindCardRecord record, String verifyCode) {
        BindConfirmReq bindApplyReq = CardConvert.INSTANCE.toCommonBindConfirm(record);
        bindApplyReq.setDynamicCode(verifyCode);
        try {
            BindConfirmRsp rsp = commonRequestService.request(bindApplyReq, new TypeReference<BindConfirmRsp>() {
            });
            record.setAgreeNo(rsp.getAgreementNoEncase());
            record.setState(ProcessState.SUCCEED);

        } catch (HttpException | JsonProcessingException | NfspBizException e) {
            logger.error("请求公共异常", e);
            record.setState(ProcessState.FAILED);
            record.setFailReason("请求公共异常: " + e.getMessage());
        }

        return record;
    }


    public BindCardRecord findBindRecordById(String recordId) {
        return bindCardRecordRepository.findById(recordId).orElseThrow(() -> new BizException(ResultCode.CARD_RECORD_NOT_EXIST));
    }

    public UserBankCard findByCardId(String cardId) {
        return userBankCardRepository.findById(cardId).orElseThrow(() -> new RuntimeException("银行卡未找到"));
    }

    private void saveSuccessRecord(BindCardRecord record) {
        UserBankCard userBankCard = CardConvert.INSTANCE.toUserBankCard(record);

        String bankName = findBankNameByAbbr(userBankCard.getBankCode());
        userBankCard.setBankName(bankName);
        userBankCardRepository.save(userBankCard);
    }

    public List<UserBankCard> findUserBankCard(String userId) {
        List<UserBankCard> byUserIdOrderByCreatedTimeDesc = userBankCardRepository.findByUserIdOrderByCreatedTimeDesc(userId);
        return byUserIdOrderByCreatedTimeDesc;
    }

    @Nullable
    public CardBin queryCardBin(String cardNo) {
        CardBin cardBin = localCardBinService.query(cardNo);
        if (Objects.isNull(cardBin) || StringUtils.isBlank(cardBin.getBankAbbr())) {
            //查询阿里云
            cardBin = alipayCardBinService.query(cardNo);
            if (cardBin != null) {
                logger.info("cardBin本地未查到，alipay查询到结果，卡号：" + cardNo + " 银行：" + cardBin.getBankAbbr());
                return cardBin;
            }
        }
        return cardBin;
    }



    public boolean queryBindCardState(UserBankCard userBankCard) {
        AgreementPayQueryBindingReq req = new AgreementPayQueryBindingReq();
        req.setWithholdAgreementNo(userBankCard.getAgreeNo());
        try {
            commonRequestService.request(req, new TypeReference<>() {
            });
            return true;
        } catch (HttpException | JsonProcessingException | NfspBizException e) {
            logger.error("请求公共异常", e);
            return false;
        }
    }
}
