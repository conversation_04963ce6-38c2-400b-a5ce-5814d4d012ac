package com.maguo.loan.cash.flow.entrance.common.dto.request.approval;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 进件请求参数
 *
 * <AUTHOR>
 * @date 2024/8/20
 */
public class ApprovalReqDTO implements Serializable {


    /**
     * 合作方单号
     */
    @NotBlank
    private String partnerOrderNo;

    /**
     * 手机号
     */
    @NotBlank
    @Length(min = 11, max = 11, message = "手机号只支持十一位")
    private String mobile;
    /**
     * 申请金额
     */
    @DecimalMin(value = "0", message = "applyAmount不为空时,必须大于0")
    @Digits(integer = 10, fraction = 2, message = "金额位数非法")
    private BigDecimal applyAmount;

    /**
     * 申请期数
     */
    @Min(1)
    private Integer applyPeriod;

    /**
     * 照片
     */
    @Valid
    @NotNull
    private Photos photos;

    /**
     * 基础信息
     */
    @Valid
    @NotNull
    private BaseInfo baseInfo;

    /**
     * 联系人
     */
    @Valid
    @NotEmpty
    @Size(min = 2, message = "最少两位联系人")
    private List<Relation> relations;

    /**
     * gps
     */
    @Valid
    private Gps gps;

    /**
     * 设备信息
     */
    private DeviceInfo deviceInfo;

    @NotBlank(message = "[imageFormat] 需要指定图片传输格式")
    private String imageFormat;

    public static class BaseInfo {
        /**
         * 教育程度
         */
        @NotBlank
        private String education;
        /**
         * 职业类别
         */
        @NotBlank
        private String position;
        /**
         * 自有车辆类型
         */
        private String car;
        /**
         * 婚姻
         */
        @NotBlank
        private String marriage;
        /**
         * 月收入
         */
        @NotNull
        @Min(value = 3000, message = "三千以下统一传递三千")
        private Integer income;
        /**
         * 行业
         */
        @NotBlank
        private String industry;
        /**
         * 住房类型
         */
        private String house;
        /**
         * 电子邮箱
         */
        @Email
        private String email;
        /**
         * 居住地址
         */
        @NotBlank
        @Length(min = 10, max = 100)
        private String livingAddress;
        /**
         * 居住街道
         */
        private String livingStreet;
        /**
         * 工作地址
         */
        @NotBlank
        @Length(min = 10, max = 100)
        private String companyAddress;
        /**
         * 工作单位
         */
        @NotBlank
        @Size(max = 200)
        private String company;
        /**
         * 工作街道
         */
        private String companyStreet;
        /**
         * 工作街道
         */
        private String companyPhone;
        /**
         * 借款用途
         */
        @NotBlank
        private String loanPurpose;

        public String getEducation() {
            return education;
        }

        public void setEducation(String education) {
            this.education = education;
        }

        public String getPosition() {
            return position;
        }

        public String getCompanyPhone() {
            return companyPhone;
        }

        public void setCompanyPhone(String companyPhone) {
            this.companyPhone = companyPhone;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public String getCar() {
            return car;
        }

        public void setCar(String car) {
            this.car = car;
        }

        public String getMarriage() {
            return marriage;
        }

        public void setMarriage(String marriage) {
            this.marriage = marriage;
        }

        public Integer getIncome() {
            return income;
        }

        public void setIncome(Integer income) {
            this.income = income;
        }

        public String getIndustry() {
            return industry;
        }

        public void setIndustry(String industry) {
            this.industry = industry;
        }

        public String getHouse() {
            return house;
        }

        public void setHouse(String house) {
            this.house = house;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getLivingAddress() {
            return livingAddress;
        }

        public void setLivingAddress(String livingAddress) {
            this.livingAddress = livingAddress;
        }

        public String getCompanyStreet() {
            return companyStreet;
        }

        public void setCompanyStreet(String companyStreet) {
            this.companyStreet = companyStreet;
        }

        public String getLivingStreet() {
            return livingStreet;
        }

        public void setLivingStreet(String livingStreet) {
            this.livingStreet = livingStreet;
        }

        public String getCompanyAddress() {
            return companyAddress;
        }

        public void setCompanyAddress(String companyAddress) {
            this.companyAddress = companyAddress;
        }

        public String getCompany() {
            return company;
        }

        public void setCompany(String company) {
            this.company = company;
        }

        public String getLoanPurpose() {
            return loanPurpose;
        }

        public void setLoanPurpose(String loanPurpose) {
            this.loanPurpose = loanPurpose;
        }
    }

    public static class Gps {
        /**
         * 维度
         */
        @DecimalMin(value = "-90", message = "Latitude must be at least -90")
        @DecimalMax(value = "90", message = "Latitude must be at most 90")
        private BigDecimal latitude;
        /**
         * 经度
         */
        @DecimalMin(value = "-180", message = "Longitude must be at least -180")
        @DecimalMax(value = "180", message = "Longitude must be at most 180")
        private BigDecimal longitude;

        public BigDecimal getLatitude() {
            return latitude;
        }

        public void setLatitude(BigDecimal latitude) {
            this.latitude = latitude;
        }

        public BigDecimal getLongitude() {
            return longitude;
        }

        public void setLongitude(BigDecimal longitude) {
            this.longitude = longitude;
        }
    }

    public static class Relation {

        /**
         * 亲属联系人
         */
        @NotBlank
        private String relation;
        /**
         * 亲属联系人姓名
         */
        @NotBlank
        private String name;
        /**
         * 紧急联系人电话
         */
        @NotBlank
        @Length(min = 11, max = 11, message = "手机号只支持十一位")
        private String phone;

        public String getRelation() {
            return relation;
        }

        public void setRelation(String relation) {
            this.relation = relation;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    public static class Photos {
        /**
         * 身份证国徽面照片
         * base64
         */
        @NotBlank
        private String nationalEmblemImage;

        /**
         * 身份证人脸照
         * base64
         */
        @NotBlank
        private String headImage;

        /**
         * 姓名
         */
        @NotBlank
        private String name;
        /**
         * 身份证号
         */
        @NotBlank
        @Size(min = 18, max = 18, message = "身份证号只支持18位")
        @Pattern(regexp = "\\d{17}[\\dXx]", message = "身份证号格式不合法")
        private String certNo;

        /**
         * 身份证有效期
         * 开始时间
         */
        @NotBlank
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式必须为 yyyy-MM-dd")
        private String certValidStart;
        /**
         * 身份证结束时间
         * 长期传递汉字
         */
        @NotBlank
        private String certValidEnd;

        /**
         * 身份证地址
         */
        @NotBlank
        @Length(min = 10, max = 100)
        private String certAddress;

        /**
         * 性别
         * 男/女
         */
        @NotBlank
        private String gender;

        /**
         * 民族
         */
        @NotBlank
        private String nation;

        /**
         * 签发机关
         */
        @NotBlank
        private String certSignOrg;
        /**
         * 活体照片
         * base64
         */
        @NotBlank
        private String faceImage;
        /**
         * 活体相似度
         */
        @NotNull
        private BigDecimal faceScore;

        /**
         * 活体供应商face++
         */
        @NotBlank
        private String faceSource;

        @NotBlank
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private String faceCollectTime;

        public String getNationalEmblemImage() {
            return nationalEmblemImage;
        }

        public void setNationalEmblemImage(String nationalEmblemImage) {
            this.nationalEmblemImage = nationalEmblemImage;
        }

        public String getHeadImage() {
            return headImage;
        }

        public void setHeadImage(String headImage) {
            this.headImage = headImage;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCertNo() {
            return certNo;
        }

        public void setCertNo(String certNo) {
            this.certNo = certNo;
        }

        public String getCertValidStart() {
            return certValidStart;
        }

        public void setCertValidStart(String certValidStart) {
            this.certValidStart = certValidStart;
        }

        public String getCertValidEnd() {
            return certValidEnd;
        }

        public void setCertValidEnd(String certValidEnd) {
            this.certValidEnd = certValidEnd;
        }

        public String getCertAddress() {
            return certAddress;
        }

        public void setCertAddress(String certAddress) {
            this.certAddress = certAddress;
        }

        public String getGender() {
            return gender;
        }

        public void setGender(String gender) {
            this.gender = gender;
        }

        public String getNation() {
            return nation;
        }

        public void setNation(String nation) {
            this.nation = nation;
        }

        public String getCertSignOrg() {
            return certSignOrg;
        }

        public void setCertSignOrg(String certSignOrg) {
            this.certSignOrg = certSignOrg;
        }

        public String getFaceImage() {
            return faceImage;
        }

        public void setFaceImage(String faceImage) {
            this.faceImage = faceImage;
        }

        public BigDecimal getFaceScore() {
            return faceScore;
        }

        public void setFaceScore(BigDecimal faceScore) {
            this.faceScore = faceScore;
        }

        public String getFaceSource() {
            return faceSource;
        }

        public void setFaceSource(String faceSource) {
            this.faceSource = faceSource;
        }

        public String getFaceCollectTime() {
            return faceCollectTime;
        }

        public void setFaceCollectTime(String faceCollectTime) {
            this.faceCollectTime = faceCollectTime;
        }
    }

    public static class DeviceInfo {
        /**
         * 设备类型
         */
        private String deviceType;
        /**
         * ios设备标识
         */
        private String idfa;
        /**
         * Android广告追踪ID
         */
        private String oaId;
        /**
         * 机型
         */
        private String model;
        /**
         * 系统类型
         */
        private String osType;
        /**
         * 系统版本
         */
        private String osVersion;
        /**
         * 是否模拟器
         */
        @Max(1)
        private String isSimulator;
        /**
         * 用户代理（User agent,简称UA）
         */
        private String ua;
        /**
         * 请求ip
         */
        private String ip;

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }

        public String getIdfa() {
            return idfa;
        }

        public void setIdfa(String idfa) {
            this.idfa = idfa;
        }

        public String getOaId() {
            return oaId;
        }

        public void setOaId(String oaId) {
            this.oaId = oaId;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getOsType() {
            return osType;
        }

        public void setOsType(String osType) {
            this.osType = osType;
        }

        public String getOsVersion() {
            return osVersion;
        }

        public void setOsVersion(String osVersion) {
            this.osVersion = osVersion;
        }

        public String getIsSimulator() {
            return isSimulator;
        }

        public void setIsSimulator(String isSimulator) {
            this.isSimulator = isSimulator;
        }

        public String getUa() {
            return ua;
        }

        public void setUa(String ua) {
            this.ua = ua;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Photos getPhotos() {
        return photos;
    }

    public void setPhotos(Photos photos) {
        this.photos = photos;
    }

    public BaseInfo getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseInfo baseInfo) {
        this.baseInfo = baseInfo;
    }

    public List<Relation> getRelations() {
        return relations;
    }

    public void setRelations(List<Relation> relations) {
        this.relations = relations;
    }

    public Gps getGps() {
        return gps;
    }

    public void setGps(Gps gps) {
        this.gps = gps;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public Integer getApplyPeriod() {
        return applyPeriod;
    }

    public void setApplyPeriod(Integer applyPeriod) {
        this.applyPeriod = applyPeriod;
    }

    public String getImageFormat() {
        return imageFormat;
    }

    public void setImageFormat(String imageFormat) {
        this.imageFormat = imageFormat;
    }
}
