package com.maguo.loan.cash.flow.service;


import com.jinghang.capital.api.dto.credit.BankCardInfoDto;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.OrderBindCardRecord;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/2
 */
@Service
public class PlatformOnceBoundService {
    private static final Logger logger = LoggerFactory.getLogger(PlatformOnceBoundService.class);

    private UserBankCardRepository userBankCardRepository;

    private OrderCardService orderCardService;


    public BankCardInfoDto obtainLoanCard(String cardId) {
        UserBankCard userBankCard = userBankCardRepository.findById(cardId).orElseThrow();
        BankCardInfoDto bankCardInfoDto = new BankCardInfoDto();
        bankCardInfoDto.setBankCode(userBankCard.getBankCode());
        bankCardInfoDto.setBankName(userBankCard.getBankName());
        bankCardInfoDto.setCardName(userBankCard.getCardName());
        bankCardInfoDto.setCardNo(userBankCard.getCardNo());
        bankCardInfoDto.setPhone(userBankCard.getPhone());
        bankCardInfoDto.setCertNo(userBankCard.getCertNo());
        bankCardInfoDto.setPayChannel(EnumConvert.INSTANCE.toCoreApi(userBankCard.getChannel()));
        bankCardInfoDto.setProtocolNo(userBankCard.getAgreeNo());
        bankCardInfoDto.setProtocolUserNo(userBankCard.getMerchantNo());
        return bankCardInfoDto;
    }

    public UserBankCard obtainRepayCard(Loan loan) {
        UserBankCard userBankCard = userBankCardRepository.findById(loan.getRepayCardId()).orElseThrow();
        OrderBindCardRecord orderBindCard = orderCardService.getOrderBindCard(loan.getOrderId());
        if (Objects.nonNull(orderBindCard)) {
            return userBankCard;
        }
        return userBankCard;

        // 已换绑, 查Loan上资方绑卡记录；未换绑，查creditId上的资方绑卡记录
//        logger.info("还款资方银行卡老逻辑:{}", loan.getId());
//        userBankCard = userBankCardRepository.getCapitalExchangeCard(loan.getId(), LoanStage.REPAY, BoundSide.CAPITAL);
//        return Objects.nonNull(userBankCard) ? userBankCard : userBankCardRepository.getCapitalCard(loan.getCreditId(), LoanStage.CREDIT, BoundSide.CAPITAL);
    }

    @Autowired
    public void setUserBankCardRepository(UserBankCardRepository userBankCardRepository) {
        this.userBankCardRepository = userBankCardRepository;
    }



    @Autowired
    public void setOrderCardService(OrderCardService orderCardService) {
        this.orderCardService = orderCardService;
    }
}
