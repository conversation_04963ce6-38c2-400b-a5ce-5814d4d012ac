package com.maguo.loan.cash.flow.entrance.cybk.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCommonResponse {
    private CYBKResponseHeader head;
    private JSONObject body;

    public CYBKResponseHeader getHead() {
        return head;
    }

    public void setHead(CYBKResponseHeader head) {
        this.head = head;
    }

    public JSONObject getBody() {
        return body;
    }

    public void setBody(JSONObject body) {
        this.body = body;
    }
}
