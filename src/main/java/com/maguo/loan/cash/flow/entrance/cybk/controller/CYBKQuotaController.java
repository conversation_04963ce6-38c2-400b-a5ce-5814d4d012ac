package com.maguo.loan.cash.flow.entrance.cybk.controller;

import com.jinghang.capital.api.QuotaService;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonRequest;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CYBKCommonResponse;
import com.maguo.loan.cash.flow.entrance.cybk.dto.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/forward/limit/adjust/notice/v2")
public class CYBKQuotaController {

    @Autowired
    private QuotaService quotaService;

    private static Logger logger = LoggerFactory.getLogger(CYBKQuotaController.class);

    /**
     * 资产正向调额结果通知
     * @param cybkCommonRequest
     * @return
     */
    @PostMapping("/mycjzygs")
    public CYBKCommonResponse upQuotaBack(@RequestBody CYBKCommonRequest cybkCommonRequest) {
        logger.info("资产正向调额结果通知接收参数：{}", JsonUtil.toJsonString(cybkCommonRequest));
        String bodyString = cybkCommonRequest.getBody().toString();
        //组装公共回调报文
        BankResultBackDto bankResultBackDto = new BankResultBackDto();
        bankResultBackDto.setBankChannel(BankChannel.CYBK);
        bankResultBackDto.setJson(bodyString);
        //回调资金系统
        bankResultBackDto = quotaService.upQuotaBack(bankResultBackDto).getData();
        //组装长银响应报文
        CYBKCommonResponse cybkCommonResponse = CommonResult.assembleResponse(cybkCommonRequest, bankResultBackDto.getJson());
        logger.info("资产正向调额结果通知响应参数：{}", JsonUtil.toJsonString(cybkCommonResponse));
        return cybkCommonResponse;
    }
}
