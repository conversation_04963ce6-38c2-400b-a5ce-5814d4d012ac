package com.maguo.loan.cash.flow.entrance.cybk.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCommonRequest {
    private CYBKRequestHeader head;
    private JSONObject body;

    public CYBKRequestHeader getHead() {
        return head;
    }

    public void setHead(CYBKRequestHeader head) {
        this.head = head;
    }

    public JSONObject getBody() {
        return body;
    }

    public void setBody(JSONObject body) {
        this.body = body;
    }
}
