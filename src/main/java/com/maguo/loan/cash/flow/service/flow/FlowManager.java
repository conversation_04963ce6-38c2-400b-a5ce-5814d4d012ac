package com.maguo.loan.cash.flow.service.flow;

import com.maguo.loan.cash.flow.enums.FlowChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
public class FlowManager {


    private Map<FlowChannel, AbstractFlowLoanService> flowLoanServices = new HashMap<>();

    public AbstractFlowLoanService getFlowLoanService(FlowChannel flowChannel) {
        return Optional.ofNullable(flowLoanServices.get(flowChannel)).orElseThrow();
    }

    @Autowired
    public void setFlowLoanServices(List<AbstractFlowLoanService> flowLoanServices) {
        this.flowLoanServices = flowLoanServices.stream().collect(Collectors.toMap(IFlowService::flowChannel, s -> s));
    }
}
