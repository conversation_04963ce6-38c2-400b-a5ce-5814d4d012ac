package com.maguo.loan.cash.flow.entrance.common.strategy;

import com.maguo.loan.cash.flow.job.jh.RepayPlanJhJob;
import com.maguo.loan.cash.flow.repository.ProjectProductMappingRepository;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class ProjectCodeMapper {

    private static final Logger logger = LoggerFactory.getLogger(RepayPlanJhJob.class);

    private final List<ProductCodeStrategy> strategies;

    @Autowired
    public ProjectCodeMapper(List<ProductCodeStrategy> strategies) {
        this.strategies = strategies;
    }

    @Autowired
    private ProjectProductMappingRepository projectProductMappingRepository;

    private final Map<String, String> mappingCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        loadMappings();
    }

    /**
     * 从数据库加载或刷新映射配置到内存缓存
     */
    public void loadMappings() {
        mappingCache.clear();
        projectProductMappingRepository.findAll().forEach(mapping ->
            mappingCache.put(mapping.getProductCode(), mapping.getProjectCode())
        );
        logger.info("加载产品项目映射配置 {} 条", mappingCache.size());
    }

    /**
     * @param source 映射来源， "FLOW", "CAPITAL"
     * @param params 包含所有相关特征的键值对
     * @return 匹配到的 project_code
     */
    public String getProjectCode(String source, Map<String, String> params) {
        // 1. 找到支持当前来源的策略
        ProductCodeStrategy strategy = strategies.stream()
            .filter(s -> s.supports(source))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("没有找到支持 '" + source + "' 来源的映射策略"));

        // 2. 使用策略构建 product_code
        String productCode = strategy.buildProductCode(params);

        // 3. 从缓存中查找 project_code
        String projectCode = mappingCache.get(productCode);
        if (projectCode == null) {
            throw new RuntimeException("未在 project_product_mapping 表中找到对应的项目编码, product_code: " + productCode);
        }
        return projectCode;
    }


}
