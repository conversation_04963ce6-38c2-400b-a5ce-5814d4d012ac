package com.maguo.loan.cash.flow.service;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.MessageSendRecord;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.SmsTemplate;
import com.maguo.loan.cash.flow.remote.nfsp.CommonRequestService;
import com.maguo.loan.cash.flow.remote.nfsp.req.SmsSendReq;
import com.maguo.loan.cash.flow.repository.MessageSendRecordRepository;
import com.maguo.loan.cash.flow.service.event.SmsSendEvent;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/10/26
 */
@Service
public class SmsService {
    private static final Logger logger = LoggerFactory.getLogger(TrialService.class);

    private ApplicationContext applicationContext;

    private WarningService warningService;

    private CommonRequestService commonRequestService;

    private MqService mqService;

    @Autowired
    private MessageSendRecordRepository messageSendRecordRepository;

    @Autowired
    private CacheService cacheService;

    public void send(@NotNull SmsTemplate smsTemplate, Map<String, String> contentParams, String receiverPhone) {

        if (AbleStatus.DISABLE.equals(smsTemplate.getAbleStatus())) {
            // 短信已禁用
            return;
        }

        Map<String, String> stringMap = new java.util.HashMap<>(Map.copyOf(contentParams));


        if (StringUtil.isNotBlank(contentParams.get("flowChannel"))) {
            String flowChannelName = contentParams.get("flowChannel");
            FlowChannel flowChannel = FlowChannel.getFlowChannel(flowChannelName);
            if (flowChannel != null) {
                stringMap.put("flowChannel", flowChannel.getAppName());
            }
        }

        if (sendSmsTemplateCheck(smsTemplate, receiverPhone)) {
            logger.info("短信模板:{},手机号: {} 已发送", smsTemplate.getDesc(), receiverPhone);
            return;
        }

        //
        applicationContext.publishEvent(new SmsSendEvent(this, smsTemplate, stringMap, receiverPhone));

        try {
            //保存短信发送记录
            MessageSendRecord messageSendRecord = new MessageSendRecord();
            messageSendRecord.setOrderNo(contentParams.get("orderNo"));
            messageSendRecord.setMessageName(smsTemplate.getDesc());
            messageSendRecord.setMessageType(smsTemplate.getTemplateNo());
            if (StringUtil.isNotBlank(contentParams.get("messageContent"))) {
                messageSendRecord.setMessageContent(contentParams.get("messageContent"));
            } else {
                messageSendRecord.setMessageContent(contentParams.toString());
            }

            messageSendRecord.setMobile(receiverPhone);
            logger.info("保存短信发送参数:{}", JsonUtil.toJsonString(messageSendRecord));
            messageSendRecordRepository.save(messageSendRecord);
        } catch (Exception e) {
            warningService.warn("短信发送记录保存异常:" + contentParams, msg -> logger.error(msg, e));
        }
    }

    public void sendSms(@NotNull SmsTemplate smsTemplate, Map<String, String> contentParams, String receiverPhone) {

        if (AbleStatus.DISABLE.equals(smsTemplate.getAbleStatus())) {
            // 短信已禁用
            return;
        }

        if (sendSmsTemplateCheck(smsTemplate, receiverPhone)) {
            logger.info("短信模板:{},手机号: {} 已发送", smsTemplate.getDesc(), receiverPhone);
            return;
        }

        applicationContext.publishEvent(new SmsSendEvent(this, smsTemplate, contentParams, receiverPhone));

        try {
            //保存短信发送记录
            MessageSendRecord messageSendRecord = new MessageSendRecord();
            messageSendRecord.setOrderNo(contentParams.get("orderNo"));
            messageSendRecord.setMessageName(smsTemplate.getDesc());
            messageSendRecord.setMessageType(smsTemplate.getTemplateNo());
            if (StringUtil.isNotBlank(contentParams.get("messageContent"))) {
                messageSendRecord.setMessageContent(contentParams.get("messageContent"));
            } else {
                messageSendRecord.setMessageContent(contentParams.toString());
            }

            messageSendRecord.setMobile(receiverPhone);
            logger.info("保存短信发送参数:{}", JsonUtil.toJsonString(messageSendRecord));
            messageSendRecordRepository.save(messageSendRecord);
        } catch (Exception e) {
            warningService.warn("短信发送记录保存异常:" + contentParams, msg -> logger.error(msg, e));
        }
    }

    /**
     * 指定场景发送
     *
     * @param smsTemplate
     * @return
     */
    private boolean sendSmsTemplateCheck(SmsTemplate smsTemplate, String receiverPhone) {
        String key = "SMS_LIMIT_SEND_" + receiverPhone + "_" + smsTemplate.name() + "_" + DateUtil.formatShort(new Date());
        Set<SmsTemplate> restrictedTemplates = Set.of(SmsTemplate.REPAY_BATCH_FAILED, SmsTemplate.REPAY_CLEAR_FAILED);
        try {
            if (restrictedTemplates.contains(smsTemplate)) {
                if (cacheService.existKey(key)) {
                    return true;
                }
                cacheService.put(key, "1", 1, TimeUnit.DAYS);
                return false;
            }
        } catch (Exception e) {
            warningService.warn("短信发送异常:" + smsTemplate.getDesc() + "_" + receiverPhone, msg -> logger.error(msg, e));
        }
        return false;
    }


    public void send2Common(SmsSendReq smsSendReq) {
        try {
            commonRequestService.request(smsSendReq, new TypeReference<>() {
            });
        } catch (Exception e) {
            warningService.warn("短信发送异常:" + smsSendReq.getTemplateId() + "," + e.getMessage(), msg -> logger.error(msg, e));
        }
    }

    /**
     * 跑批使用
     *
     * @param smsTemplate
     * @param contentParams
     * @param receiverPhone
     */
    public void sendForJob(@NotNull SmsTemplate smsTemplate, Map<String, String> contentParams, String receiverPhone) {

        if (AbleStatus.DISABLE.equals(smsTemplate.getAbleStatus())) {
            // 短信已禁用
            return;
        }
        Map<String, String> stringMap = new java.util.HashMap<>(Map.copyOf(contentParams));


        if (StringUtil.isNotBlank(contentParams.get("flowChannel"))) {
            String flowChannelName = contentParams.get("flowChannel");
            FlowChannel flowChannel = FlowChannel.getFlowChannel(flowChannelName);
            if (flowChannel != null) {

                stringMap.put("flowChannel", flowChannel.getAppName());
            }
        }

        SmsSendReq smsSendReq = new SmsSendReq();
        smsSendReq.setType(smsTemplate.getSmsType());
        smsSendReq.setTemplateId(smsTemplate.getTemplateNo());
        smsSendReq.setMessageParamMap(stringMap);
        smsSendReq.setPhoneList(List.of(receiverPhone));
        //
        mqService.submitSmsSend(JSON.toJSONString(smsSendReq));

    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Autowired
    public void setWarningService(WarningService warningService) {
        this.warningService = warningService;
    }

    @Autowired
    public void setCommonRequestService(CommonRequestService commonRequestService) {
        this.commonRequestService = commonRequestService;
    }

    @Autowired
    public void setMqService(MqService mqService) {
        this.mqService = mqService;
    }
}
