package com.maguo.loan.cash.flow.service;


import com.jinghang.capital.api.PlanService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.repay.PlanDto;
import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.jinghang.capital.api.dto.repay.PlanQueryDto;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.convert.RepayConvert;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.remote.core.FinRepayService;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.rate.RateManager;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/10/13
 */
@Service
public class RepayPlanService {
    private static final Logger logger = LoggerFactory.getLogger(RepayPlanService.class);

    private FinRepayService finRepayService;

    private WarningService warningService;

    private LoanRepository loanRepository;

    private RepayPlanRepository repayPlanRepository;

    private CustomRepayRecordRepository customRepayRecordRepository;

    private OrderRepository orderRepository;


    @Autowired
    private PlanService planService;

    @Autowired
    private RateManager rateManager;

    public List<RepayPlan> generateRepayPlan(Loan loan) {
        PlanQueryDto planQueryDto = new PlanQueryDto();
        planQueryDto.setSysLoanId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        planQueryDto.setLoanId(loan.getLoanNo());
        // 查询core还款计划
        RestResult<PlanDto> restResult = finRepayService.queryPlan(planQueryDto);
        logger.info("查询core还款计划返回:{}", JsonUtil.toJsonString(restResult));
        if (!restResult.isSuccess()) {
            warningService.warn("查询core还款计划异常:" + loan.getId(), msg -> logger.error("查询core还款计划异常:{}", JsonUtil.toJsonString(restResult)));
            return null;
        }
        if (CollectionUtil.isEmpty(restResult.getData().getPlanItems())) {
            warningService.warn("查询core还款计划为空:" + loan.getId(), logger::error);
            return null;
        }

        List<RepayPlan> customRepayPlans = new ArrayList<>();
        //剩余本金
        BigDecimal remainingPrincipalAmt = restResult.getData().getLoanAmt();
        List<PlanItemDto> planItemList = restResult.getData().getPlanItems().stream().sorted(Comparator.comparing(PlanItemDto::getPeriod)).toList(); //还款计划
        LocalDateTime loanTime = loan.getLoanTime();
        LocalDate date = loanTime.toLocalDate();

        // todo 28号可能有修改
        for (PlanItemDto planItemDto : planItemList) {
            //保存上期的天数
            long dateNum = DateUtil.dateDiff(date, planItemDto.getRepayDate());
            date=planItemDto.getRepayDate();
            RepayPlan repayPlan = new RepayPlan();
            repayPlan.setUserId(loan.getUserId());
            repayPlan.setLoanId(loan.getId());
            repayPlan.setPeriod(planItemDto.getPeriod());
            repayPlan.setPlanRepayDate(planItemDto.getRepayDate());
            repayPlan.setCustRepayState(RepayState.NORMAL);
            // 资方本金
            repayPlan.setPrincipalAmt(planItemDto.getPrincipalAmt());
            // 资方利息
            repayPlan.setInterestAmt(planItemDto.getInterestAmt());
            //罚息
            repayPlan.setPenaltyAmt(planItemDto.getPenaltyAmt());
            // 融担费
            repayPlan.setGuaranteeAmt(BigDecimal.ZERO);
            // 咨询费
            repayPlan.setConsultFee(calConsultFee(loan, planItemDto, remainingPrincipalAmt,dateNum));
            // 月供 本+利+罚+咨询
            repayPlan.setAmount(repayPlan.getPrincipalAmt().add(repayPlan.getInterestAmt()).add(repayPlan.getPenaltyAmt()).add(repayPlan.getConsultFee()));
            //
            customRepayPlans.add(repayPlan);
            // 剩余本金
            remainingPrincipalAmt = remainingPrincipalAmt.subtract(planItemDto.getPrincipalAmt());
        }
        logger.info("对客还款计划:{}", JsonUtil.toJsonString(customRepayPlans));
        return repayPlanRepository.saveAll(customRepayPlans);
    }

    /**
     * 还款计划咨询费
     *
     * @param loan
     * @param planItemDto
     * @param remainingPrincipalAmt
     * @return
     */
    private BigDecimal calConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt,long num) {
        Order order = orderRepository.findById(loan.getOrderId()).orElseThrow();
        // 风控输出IRR24,不需产生咨询费
        return rateManager.getRateServices(order.getApproveRate()).planConsultFee(loan, planItemDto, remainingPrincipalAmt,num);
    }
    public List<RepayPlan> findRepayPlanByLoanId(String loanId) {
        return repayPlanRepository.findByLoanIdOrderByPeriod(loanId);
    }

    /**
     * 是否存在待还款，还款计划
     */
    public boolean exists(String loanId, RepayState custRepayState) {
        return repayPlanRepository.existsAllByLoanIdAndCustRepayStateOrderByPeriodAsc(loanId, custRepayState);
    }


    public RepayPlan findById(String id) {
        return repayPlanRepository.findById(id).orElseThrow();
    }

    /**
     * 判断是否需要对应还金额 缩期
     *
     * @param repayPlan
     * @return true：需要缩期，false：不需要缩期
     */
    public boolean judgementReducePeriod(RepayPlan repayPlan) {
        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findTopByLoanIdAndPeriodOrderByCreatedTimeDesc(
            repayPlan.getLoanId(), repayPlan.getPeriod()).orElse(null);

        //判断是否需要缩期：对客还款记录 是结清、且状态是处理中、或已完成
        if (AmountUtil.safeAmount(repayPlan.getActAmount()).compareTo(BigDecimal.ZERO) > 0
            && Objects.nonNull(customRepayRecord)
            && RepayPurpose.CLEAR == customRepayRecord.getRepayPurpose()
            && StringUtils.equalsAny(customRepayRecord.getRepayState().name(), ProcessState.PROCESSING.name(), ProcessState.SUCCEED.name())) {
            return true;
        }
        return false;
    }

    /**
     * 应还金额 缩期处理
     *
     * @param repayPlan 当期还款计划
     * @return 缩期后的还款计划
     */
    public RepayPlan reducePeriod(RepayPlan repayPlan) {
        if (!judgementReducePeriod(repayPlan)) {
            //本期不需要缩期，直接返回null，由各流量做后续处理
            return null;
        }
        RepayPlan newPlan = RepayConvert.INSTANCE.copy(repayPlan);
        //应还本金
        BigDecimal principal = AmountUtil.safeAmount(repayPlan.getPrincipalAmt());
        //应还利息 = 按日计息 = 实际还成功的利息
        BigDecimal interest = AmountUtil.safeAmount(repayPlan.getActInterestAmt());
        //应还罚息(逾期违约金）
        BigDecimal penalty = AmountUtil.safeAmount(repayPlan.getActPenaltyAmt());
        //应还融担费
        BigDecimal guaranteeFee = AmountUtil.safeAmount(repayPlan.getActGuaranteeAmt());
        //应还咨询费(本期+之后所有的咨询费)
        BigDecimal consultFee = AmountUtil.safeAmount(repayPlan.getConsultFee());

        List<RepayPlan> repayPlanList = repayPlanRepository.findByLoanId(repayPlan.getLoanId());
        List<RepayPlan> list = repayPlanList.stream().filter(item -> item.getPeriod() > repayPlan.getPeriod()).toList();
        for (RepayPlan item : list) {
            principal = principal.add(AmountUtil.safeAmount(item.getPrincipalAmt()));
            guaranteeFee = guaranteeFee.add(AmountUtil.safeAmount(item.getActGuaranteeAmt()));
            consultFee = consultFee.add(AmountUtil.safeAmount(item.getConsultFee()));
        }
        //应还总金额
        BigDecimal amount = principal.add(interest).add(penalty).add(guaranteeFee).add(consultFee);
        newPlan.setInterestAmt(interest);
        newPlan.setPrincipalAmt(principal);
        newPlan.setPenaltyAmt(penalty);
        newPlan.setGuaranteeAmt(guaranteeFee);
        newPlan.setConsultFee(consultFee);
        newPlan.setAmount(amount);
        return newPlan;
    }

    public void repayPlanSyncToCore(String loanId) {
        Loan loan = loanRepository.findById(loanId).orElseThrow();
        List<RepayPlan> repayPlans = repayPlanRepository.findByLoanIdOrderByPeriod(loanId);

        PlanDto planDto = new PlanDto();
        planDto.setChannel(loan.getBankChannel());
        planDto.setPeriods(loan.getPeriods());
        planDto.setSysLoanId(loan.getId());
        planDto.setLoanId(loan.getLoanNo());
        planDto.setLoanAmt(loan.getAmount());
        planDto.setPlanItems(getPlanItemDtoList(repayPlans));
        RestResult<Void> result = planService.detailPush(planDto);
        if (!result.isSuccess()) {
            warningService.warn("还款计划同步 fin-core 失败 loanId: " + loanId);
        }
    }

    private List<PlanItemDto> getPlanItemDtoList(List<RepayPlan> repayPlans) {
        List<PlanItemDto> planItemDtoList = new ArrayList<>();
        for (RepayPlan repayPlan : repayPlans) {
            PlanItemDto planItemDto = new PlanItemDto();
            planItemDto.setPeriod(repayPlan.getPeriod());
            planItemDto.setRepayDate(repayPlan.getPlanRepayDate());
            planItemDto.setTotalAmt(repayPlan.getAmount());
            planItemDto.setPrincipalAmt(repayPlan.getPrincipalAmt());
            planItemDto.setInterestAmt(repayPlan.getInterestAmt());
            planItemDto.setPenaltyAmt(repayPlan.getPenaltyAmt());
            planItemDto.setGuaranteeAmt(repayPlan.getGuaranteeAmt());
            planItemDto.setConsultAmt(repayPlan.getConsultFee());
            planItemDtoList.add(planItemDto);
        }
        return planItemDtoList;
    }

    @Autowired
    public void setFinRepayService(FinRepayService finRepayService) {
        this.finRepayService = finRepayService;
    }

    @Autowired
    public void setWarningService(WarningService warningService) {
        this.warningService = warningService;
    }

    @Autowired
    public void setRepayPlanRepository(RepayPlanRepository repayPlanRepository) {
        this.repayPlanRepository = repayPlanRepository;
    }

    @Autowired
    public void setCustomerRepayPlanRepository(CustomRepayRecordRepository customRepayRecordRepository) {
        this.customRepayRecordRepository = customRepayRecordRepository;
    }

    @Autowired
    public void setLoanRepository(LoanRepository loanRepository) {
        this.loanRepository = loanRepository;
    }

    @Autowired
    public void setOrderRepository(OrderRepository orderRepository) {
        this.orderRepository = orderRepository;
    }

}
