package com.maguo.loan.cash.flow.service.event.listener;


import com.maguo.loan.cash.flow.entity.RiskRevolvingRecord;
import com.maguo.loan.cash.flow.entity.UserRevolving;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.RevolvingState;
import com.maguo.loan.cash.flow.repository.RiskRevolvingRecordRepository;
import com.maguo.loan.cash.flow.repository.UserRevolvingRepository;
import com.maguo.loan.cash.flow.service.UserRevolvingService;
import com.maguo.loan.cash.flow.service.event.RiskRevolvingAmountEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-02-23
 */
@Component
public class RiskRevolvingAmountEventListener {

    private static final Logger logger = LoggerFactory.getLogger(RiskRevolvingAmountEventListener.class);

    @Autowired
    private RiskRevolvingRecordRepository riskRevolvingRecordRepository;

    @Autowired
    private UserRevolvingRepository userRevolvingRepository;

    @Autowired
    private UserRevolvingService userRevolvingService;

    private static final int AMOUNT_EXPIRE_MONTH = 1;

    @EventListener(RiskRevolvingAmountEvent.class)
    public void onApplicationEvent(RiskRevolvingAmountEvent amountEvent) {
        String riskRecordId = amountEvent.getRevolvingRecordId();
        RiskRevolvingRecord revolvingRecord = riskRevolvingRecordRepository.findById(riskRecordId).orElseThrow();
        try {
            AuditState auditState = revolvingRecord.getApproveResult();
            UserRevolving userRevolving = userRevolvingRepository.findById(revolvingRecord.getUserId()).orElseThrow();
            if (auditState == AuditState.PASS) {
                // 拆箱
                BigDecimal amount = userRevolvingService.revolvingReleateHandler(
                        revolvingRecord.getApproveAmount(),
                        userRevolving, false);
                userRevolving.setRevolvingState(RevolvingState.NORMAL);
                userRevolving.setApproveAmount(amount);
                userRevolving.setAmountExpireTime(LocalDateTime.now().plusMonths(AMOUNT_EXPIRE_MONTH));
                userRevolvingRepository.save(userRevolving);
            }
            if (auditState == AuditState.REJECT) {
                userRevolving.setRevolvingState(RevolvingState.INVALID);
                userRevolvingRepository.save(userRevolving);
            }
        } catch (Exception e) {
            logger.error("风控结果异步更新 revolvingAmount 失败", e);
        }
    }
}
