package com.maguo.loan.cash.flow.service;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.ThirdUserInfo;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.ThirdChannel;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.ThirdUserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024-12-12
 */
@Service
public class MiniMsgSubscribeService {

    private static final Logger logger = LoggerFactory.getLogger(MiniMsgSubscribeService.class);

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private ThirdUserInfoRepository thirdUserInfoRepository;


    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String RISK_RESULT_SUCCESS_CONTENT = "恭喜您，成功获得额度%s元";
    private static final String RISK_RESULT_FAIL_CONTENT = "很遗憾，因您当前资质不符，没有通过审核。";
    private static final String RISK_RESULT_SUCCESS_STATUS = "审核成功";
    private static final String RISK_RESULT_FAIL_STATUS = "审核失败";
    private static final String RISK_RESULT_SUCCESS_DESCRIPTION = "额度获取当天提现成功率更高哦~";
    private static final String RISK_RESULT_FAIL_DESCRIPTION = "请您在30天后再次尝试，届时您的申请条件可能会有所改善。";
    private static final String VALUE_KEY = "value";

    public void send(String riskId) {
        UserRiskRecord riskRecord = userRiskRecordRepository.findById(riskId).orElseThrow();
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElseThrow();
        if (preOrder.getApplicationSource() == ApplicationSource.MINI_WECHAT_QHYX) {
            Optional<ThirdUserInfo> thirdUserInfoOptional = thirdUserInfoRepository
                .findByUserIdAndMobileAndThirdChannel(riskRecord.getUserId(), preOrder.getMobile(), ThirdChannel.WECHAT);
            if (thirdUserInfoOptional.isPresent()) {
                Map<String, Object> params = wechatMsgParams(riskRecord);
                //风控平台审核通过，发送风控平台消息
                logger.info("发送风控结果消息，{}", JsonUtil.toJsonString(params));
//                wechatService.sendSubscribe(qhyxWechatAppid, qhyxWechatSecret, thirdUserInfoOptional.get().getThirdUserId(), riskResultTemplateId, params);
            }
        }

    }

    @NotNull
    private static Map<String, Object> wechatMsgParams(UserRiskRecord riskRecord) {
        Map<String, Object> params = new HashMap<>();
        // 状态
        Map<String, String> status = new HashMap<>();
        status.put(VALUE_KEY, riskRecord.getApproveResult() == AuditState.PASS
            ? RISK_RESULT_SUCCESS_STATUS
            : RISK_RESULT_FAIL_STATUS);
        params.put("phrase1", status);
        // 内容
        Map<String, String> title = new HashMap<>();
        title.put(VALUE_KEY, riskRecord.getApproveResult() == AuditState.PASS
            ? String.format(RISK_RESULT_SUCCESS_CONTENT, riskRecord.getApproveAmount().toString())
            : RISK_RESULT_FAIL_CONTENT);
        params.put("thing2", title);
        // 时间
        Map<String, String> time = new HashMap<>();
        time.put(VALUE_KEY, riskRecord.getUpdatedTime().format(FORMATTER));
        params.put("date3", time);
        // 描述
        Map<String, String> desc = new HashMap<>();
        desc.put(VALUE_KEY, riskRecord.getApproveResult() == AuditState.PASS
            ? RISK_RESULT_SUCCESS_DESCRIPTION
            : RISK_RESULT_FAIL_DESCRIPTION);
        params.put("thing4", desc);
        return params;
    }
}
