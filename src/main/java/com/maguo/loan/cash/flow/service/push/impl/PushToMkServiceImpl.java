package com.maguo.loan.cash.flow.service.push.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.maguo.loan.cash.flow.dto.pushmk.CustomerInfoPushDto;
import com.maguo.loan.cash.flow.dto.pushmk.LoanApplyLog;
import com.maguo.loan.cash.flow.dto.pushmk.RepayRefInfo;
import com.maguo.loan.cash.flow.dto.pushmk.RepaymentPlan;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.exception.ShmBusinessException;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.service.push.AbstractPushToMkService;
import com.maguo.loan.cash.flow.util.AgeUtil;
import com.maguo.loan.cash.flow.util.PushMkConvertUtil;
import com.maguo.loan.cash.flow.util.SerialNumGenerator;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class PushToMkServiceImpl extends AbstractPushToMkService {

    private static final Logger log = LoggerFactory.getLogger(PushToMkServiceImpl.class);


    @Value("${shm.push.submit.url}")
    private String submitUrl;
    @Value("${shm.push.submit.des}")
    private String des;


    @Resource
    private RepayPlanRepository repayPlanRepository;

    @Resource
    private UserInfoRepository userInfoRepository;

    @Resource
    private UserOcrRepository userOcrRepository;

    @Resource
    private LoanRepository loanRepository;

    @Override
    public void execute(String loanId) {
        super.execute(loanId);
    }

    @Override
    protected String getBody(String loanId) {
        log.info("组装报文---开始：{}", loanId);
        CustomerInfoPushDto customerInfoPushDto = new CustomerInfoPushDto();
        List<RepayPlan> repayPlans = repayPlanRepository.findByLoanIdOrderByPeriod(loanId);
        if (CollectionUtils.isEmpty(repayPlans)) {
            throw new ShmBusinessException("该笔数据未查询到还款计划:" + loanId);
        }
        customerInfoPushDto.setLoanslipNumber(loanId);

        List<RepayRefInfo> repayRefList = repayPlans.stream()
            .map(repayPlan -> {
                RepayRefInfo info = new RepayRefInfo();
                info.setPeriodNum(ObjectUtils.isEmpty(repayPlan.getPeriod()) ? null : repayPlan.getPeriod().toString());
                // info.setStartDate()无起息日
                info.setSettleDate(ObjectUtils.isEmpty(repayPlan.getPlanRepayDate()) ? null : repayPlan.getPlanRepayDate().toString());// 应还日期 (yyyy-MM-dd) 传计划还款日
                info.setTradeDate(ObjectUtils.isEmpty(repayPlan.getActRepayTime()) ? null : repayPlan.getActRepayTime().toLocalDate().toString());// 还款日期 (yyyy-MM-dd) 传实还时间
                info.setNeedAmount(repayPlan.getAmount());
                info.setNeedCorpus(repayPlan.getPrincipalAmt());
                info.setNeedAccrual(repayPlan.getInterestAmt());
                info.setNeedPunish(repayPlan.getPenaltyAmt());
                // info.setNeedRecvcomp();//无 应还复息
                info.setAlreadyAmount(repayPlan.getActAmount());
                info.setAlreadyCorpus(repayPlan.getActPrincipalAmt());
                info.setAlreadyAccrual(repayPlan.getActInterestAmt());
                info.setAlreadyPunish(repayPlan.getActPenaltyAmt());
                // info.setAlreadyRecvcomp();//无 已还复息
                // info.setCorpusStatus();// 无 本金状态
                info.setNeedFee(repayPlan.getConsultFee());
                info.setAlreadyFee(ObjectUtils.isEmpty(repayPlan.getActConsultFee()) ? BigDecimal.ZERO : repayPlan.getActConsultFee());
                return info;
            })
            .collect(Collectors.toList());
        RepaymentPlan repaymentPlan = new RepaymentPlan();
        repaymentPlan.setTotalPeriod(repayPlans.size());
        repaymentPlan.setRepayRefList(repayRefList);

        ArrayList<RepaymentPlan> repaymentPlans = new ArrayList<>();
        repaymentPlans.add(repaymentPlan);
        customerInfoPushDto.setRepaymentPlan(repaymentPlans);


        customerInfoPushDto.setTransactionID(SerialNumGenerator.generateSerial());
        String userId = repayPlans.get(0).getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            throw new ShmBusinessException("还款计划数据中客户id缺失:" + loanId);
        }

        UserOcr userOcr = userOcrRepository.findByUserId(userId);
        if (!ObjectUtils.isEmpty(userOcr)) {
            customerInfoPushDto.setAge(AgeUtil.calculateAge(userOcr.getCertNo()));
            customerInfoPushDto.setIDCardNumber(userOcr.getCertNo());
            customerInfoPushDto.setEthnicity(userOcr.getNation());
            customerInfoPushDto.setSex(PushMkConvertUtil.genderToSex(userOcr.getGender()));
            customerInfoPushDto.setIDCardType("1");
            customerInfoPushDto.setIDCardAddress(userOcr.getCertAddress());
            customerInfoPushDto.setHouseholdAddress(userOcr.getCertAddress());
            customerInfoPushDto.setIDCardEndDate(ObjectUtils.isEmpty(userOcr.getCertValidEnd()) ? null : userOcr.getCertValidEnd().toString());
            UserInfo userInfo = userInfoRepository.findByCertNo(userOcr.getCertNo());
            if (!ObjectUtils.isEmpty(userInfo)) {
                customerInfoPushDto.setEducationalBackground(PushMkConvertUtil.educationToBackgroundCode(userInfo.getEducation()));
                customerInfoPushDto.setEmail(userInfo.getEmail());
                customerInfoPushDto.setMarriage(PushMkConvertUtil.marriageTo(userInfo.getMarriage()));
                customerInfoPushDto.setName(userInfo.getName());
                customerInfoPushDto.setPhoneNumber(userInfo.getMobile());
                customerInfoPushDto.setResidenceAddress(userInfo.getLivingAddress());
            }
        }


        List<Loan> allByUserId = loanRepository.findAllByUserId(userId);
        if (!ObjectUtils.isEmpty(allByUserId)) {
            List<LoanApplyLog> loanApplyLogList = allByUserId.stream()
                .map(order -> {
                    LoanApplyLog loanApplyLog = new LoanApplyLog();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    loanApplyLog.setTime(ObjectUtils.isEmpty(order.getCreatedTime()) ? null : order.getCreatedTime().format(formatter));
                    loanApplyLog.setChannel(ObjectUtils.isEmpty(order.getBankChannel()) ? null : order.getBankChannel().getName());
                    loanApplyLog.setProd(ObjectUtils.isEmpty(order.getFlowChannel()) ? null : order.getFlowChannel().getDesc());
                    loanApplyLog.setApprovalResult(ObjectUtils.isEmpty(order.getLoanState()) ? null : order.getLoanState().toString());
                    loanApplyLog.setRefusalReason(order.getFailReason());
                    loanApplyLog.setAmount(order.getAmount());
                    loanApplyLog.setPurpose(ObjectUtils.isEmpty(order.getLoanPurpose()) ? null : order.getLoanPurpose().getDesc());
                    loanApplyLog.setCycle(ObjectUtils.isEmpty(order.getPeriods()) ? null : order.getPeriods().toString());
                    return loanApplyLog;
                })
                .collect(Collectors.toList());
            customerInfoPushDto.setLoanApplyLog(loanApplyLogList);
        }

        // customerInfoPushDto.setLoanType();
        // customerInfoPushDto.setServiceList();
        // customerInfoPushDto.setCreditReport();
        // customerInfoPushDto.setFinance();
        SerializeConfig config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.PascalCase;
        log.info("组装报文---完成：{}", loanId);
        return JSON.toJSONString(customerInfoPushDto, config);
    }

    @Override
    protected String getUrl() {
        return submitUrl;
    }


    @Override
    protected String msgEncrypt(String msg) {
        log.info("请求明文为：{}", msg);
        try {
            // 1. 计算MD5并处理密钥
            String md5Key = md5(des).toUpperCase().substring(0, 8);
            byte[] keyBytes = md5Key.getBytes(StandardCharsets.UTF_8);

            // 2. 初始化DES加密器
            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "DES");
            IvParameterSpec iv = new IvParameterSpec(keyBytes);
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

            // 3. 执行加密
            byte[] textBytes = msg.getBytes(StandardCharsets.UTF_8);
            byte[] encrypted = cipher.doFinal(textBytes);

            // 4. 转换为十六进制字符串
            return bytesToHex(encrypted);
        } catch (Exception e) {
            log.error("DES Encryption failed", e);
            throw new ShmBusinessException("DES Encryption failed");
        }
    }

    private static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("MD5 calculation failed", e);
            throw new ShmBusinessException("MD5 calculation failed");
        }
    }


    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b)); // 大写 ,与C#的{0:X2}一致
        }
        return sb.toString();
    }

}
