package com.maguo.loan.cash.flow.service.event.listener;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.RepayGuaranteeFeeService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.banks.BankManager;
import com.maguo.loan.cash.flow.service.event.PrincipalRepaySucceedEvent;
import com.maguo.loan.cash.flow.service.event.RepaySucceedResultEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 本息还款成功事件
 */
@Component
public class PrincipalRepaySucceedListener {

    private static final Logger logger = LoggerFactory.getLogger(PrincipalRepaySucceedListener.class);


    @Autowired
    private LockService lockService;


    public static final int LOCK_WAIT_SECOND = 3;

    public static final int LOCK_RELEASE_SECOND = 8;

    @Qualifier("warningService")
    @Autowired
    private WarningService warningService;


    @Autowired
    private LoanRepository loanRepository;


    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private RepayGuaranteeFeeService repayGuaranteeFeeService;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private BankManager bankManager;

    @Autowired
    private RepayExtraGuaranteePlanRepository feePlanRepository;


    @EventListener(PrincipalRepaySucceedEvent.class)
    public void online(PrincipalRepaySucceedEvent principalSucceedEvent) {
        logger.info("处理本息成功事件:{}", JsonUtil.toJsonString(principalSucceedEvent));

        CustomRepayRecord customRepayRecord = customRepayRecordRepository.findById(principalSucceedEvent.getCustomRepayRecordId())
                .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        updateSuccessRecordAndPlan(customRepayRecord);
        eventPublisher.publishEvent(new RepaySucceedResultEvent(customRepayRecord.getId()));


    }

    /**
     * 更新还款计划&还款计划
     */
    private void updateSuccessRecordAndPlan(CustomRepayRecord repayRecord) {
        repayRecord.setRepayState(ProcessState.SUCCEED);
        customRepayRecordRepository.save(repayRecord);

        List<RepayPlan> planList = repayPlanRepository.findByLoanIdOrderByPeriod(repayRecord.getLoanId());
        final int curPeriod = repayRecord.getPeriod();
        RepayPurpose purpose = repayRecord.getRepayPurpose();
        RepayPlan curPlan =
                planList.stream().filter(p -> p.getPeriod().compareTo(curPeriod) == 0).findFirst().orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
        curPlan.setCustRepayState(RepayState.REPAID);
        repayPlanRepository.save(curPlan);

        if (RepayPurpose.CLEAR == purpose) {
            planList.stream().filter(p -> p.getPeriod() > curPeriod).forEach(p -> {
                p.setActAmount(BigDecimal.ZERO);
                p.setActPrincipalAmt(BigDecimal.ZERO);
                p.setActInterestAmt(BigDecimal.ZERO);
                p.setActPenaltyAmt(BigDecimal.ZERO);
                p.setActGuaranteeAmt(BigDecimal.ZERO);
                p.setActBreachAmt(BigDecimal.ZERO);
                p.setActConsultFee(BigDecimal.ZERO);
                p.setActRepayTime(curPlan.getActRepayTime());
                p.setCustRepayState(RepayState.REPAID);
                repayPlanRepository.save(p);
            });
        }
    }

}
