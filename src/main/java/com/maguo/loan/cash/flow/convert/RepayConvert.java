package com.maguo.loan.cash.flow.convert;

import com.jinghang.capital.api.dto.repay.RepayApplyDto;
import com.jinghang.capital.api.dto.repay.TrailResultDto;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.entity.WithholdShareInfo;
import com.maguo.loan.cash.flow.remote.nfsp.req.ChargeApplyReq;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RepayConvert {

    RepayConvert INSTANCE = Mappers.getMapper(RepayConvert.class);

    @Mapping(target = "amount", ignore = true)
    @Mapping(target = "guaranteeFee", ignore = true)
    @Mapping(source = "guaranteeFee", target = "capitalGuaranteeFee", defaultValue = "0")
    @Mapping(source = "overdueFee", target = "capitalPenalty", defaultValue = "0")
    TrialResultVo toVo(TrailResultDto dto);

    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(source = "id", target = "repayRecordId")
    WithholdFlow toWithholdFlow(CustomRepayRecord record);




    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "remark", ignore = true)
    // 非中置-辽农 对资表不需要咨询费
    @Mapping(target = "consultFee", ignore = true)
    @Mapping(source = "id", target = "sourceRecordId")
    @Mapping(source = "principalAmt", target = "principal")
    @Mapping(source = "interestAmt", target = "interest")
    @Mapping(source = "capitalGuaranteeAmt", target = "guarantee")
    @Mapping(source = "capitalPenaltyAmt", target = "penalty")
    @Mapping(source = "breachAmt", target = "breach")
    BankRepayRecord toBankRepayRecord(CustomRepayRecord customRepayRecord);

    @Mapping(source = "id", target = "outerRepayId")
    @Mapping(source = "guarantee", target = "guaranteeFee")
    @Mapping(source = "breach", target = "breachFee")
    @Mapping(source = "penalty", target = "overdueFee")
    RepayApplyDto toRepayApplyDto(BankRepayRecord customRepayRecord);

    List<ChargeApplyReq.ShareInfo> coverWithholdShareInfoToShareInfo(List<WithholdShareInfo> list);

    @Mapping(source = "merchantNo", target = "shareMchId")
    @Mapping(source = "amount", target = "shareAmount")
    ChargeApplyReq.ShareInfo coverWithholdShareInfoToShareInfo(WithholdShareInfo withholdShareInfo);



    RepayPlan copy(RepayPlan plan);


}

