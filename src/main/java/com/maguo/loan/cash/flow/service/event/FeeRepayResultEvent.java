package com.maguo.loan.cash.flow.service.event;


import com.maguo.loan.cash.flow.enums.ProcessState;

/**
 * @Classname GuaranteeChargeResultEvent
 * @Description 融担费贷款结果处理
 * @Date 2023/10/24 20:43
 * @Created by gale
 */
public class FeeRepayResultEvent {


    private String recordId;
    private ProcessState repayState;
    private String failReason;


    public FeeRepayResultEvent(String recordId, ProcessState repayState, String failReason) {
        this.recordId = recordId;
        this.repayState = repayState;
        this.failReason = failReason;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public ProcessState getRepayState() {
        return repayState;
    }

    public void setRepayState(ProcessState repayState) {
        this.repayState = repayState;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }
}
