package com.maguo.loan.cash.flow.entrance.cybk.filter.utils;

import com.maguo.loan.cash.flow.entrance.cybk.exception.CYBKBizException;
import com.maguo.loan.cash.flow.entrance.cybk.enums.CYBKResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AESUtil {
    private static Logger log = LoggerFactory.getLogger(AESUtil.class);

    public AESUtil() {
    }

    public static byte[] encrypt(byte[] byteContent, String password) {
        try {
            byte[] pwdBytes = password.getBytes("utf-8");
            SecretKeySpec secretKey = new SecretKeySpec(pwdBytes, "AES");
            byte[] initParam = "A-16-Byte-String".getBytes("utf-8");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(1, secretKey, ivParameterSpec);
            byte[] result = cipher.doFinal(byteContent);
            return result;
        } catch (Exception var8) {
            log.error("加密发生异常", var8);
            throw new CYBKBizException(CYBKResultCode.SM4_ERROR);
        }
    }

    public static byte[] decrypt(byte[] byteContent, String password) {
        try {
            byte[] pwdBytes = password.getBytes("utf-8");
            SecretKeySpec secretKey = new SecretKeySpec(pwdBytes, "AES");
            byte[] initParam = "A-16-Byte-String".getBytes("utf-8");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(2, secretKey, ivParameterSpec);
            byte[] result = cipher.doFinal(byteContent);
            return result;
        } catch (Exception var8) {
            log.error("解密发生异常", var8);
            throw new CYBKBizException(CYBKResultCode.SM4_ERROR);
        }
    }

    public static String encrypt(String content, String password) {
        try {
            byte[] byteContent = content.getBytes("utf-8");
            byte[] pwdBytes = password.getBytes("utf-8");
            SecretKeySpec secretKey = new SecretKeySpec(pwdBytes, "AES");
            byte[] initParam = "A-16-Byte-String".getBytes("utf-8");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(1, secretKey, ivParameterSpec);
            byte[] result = cipher.doFinal(byteContent);
            return Base64.encodeBase64(result);
        } catch (Exception var9) {
            log.error("加密发生异常", var9);
            throw new CYBKBizException(CYBKResultCode.SM4_ERROR);
        }
    }

    public static String decrypt(String content, String password) {
        try {
            byte[] byteContent = Base64.decodeBase64(content);
            byte[] pwdBytes = password.getBytes("utf-8");
            SecretKeySpec secretKey = new SecretKeySpec(pwdBytes, "AES");
            byte[] initParam = "A-16-Byte-String".getBytes("utf-8");
            IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(2, secretKey, ivParameterSpec);
            byte[] result = cipher.doFinal(byteContent);
            return new String(result, "utf-8");
        } catch (Exception var9) {
            log.error("解密发生异常", var9);
            throw new CYBKBizException(CYBKResultCode.SM4_ERROR);
        }
    }
}
