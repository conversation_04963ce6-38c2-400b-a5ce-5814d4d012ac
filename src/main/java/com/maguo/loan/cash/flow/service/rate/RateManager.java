package com.maguo.loan.cash.flow.service.rate;

import com.maguo.loan.cash.flow.enums.RateLevel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 利率管理
 *
 * <AUTHOR>
 */
@Service
public class RateManager {

    private Map<RateLevel, IRateService> rateServices = new HashMap<>();



    @Autowired
    public void setBankLoanServices(List<IRateService> rateServices) {
        this.rateServices = rateServices.stream().collect(Collectors.toMap(IRateService::rateLevel, s -> s));
    }

    public IRateService getRateServices(RateLevel rateLevel) {
        return rateServices.get(rateLevel);
    }

}
