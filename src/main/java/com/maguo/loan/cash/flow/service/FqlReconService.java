package com.maguo.loan.cash.flow.service;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.FqlRepayApplyRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.fql.FqlLoanVo;
import com.maguo.loan.cash.flow.entity.fql.FqlRepayDetailVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.FqlRepayApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 处理鲸航-绿信对账文件
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/05/29 11:31
 */
@Service
public class FqlReconService {

    private static final Logger logger = LoggerFactory.getLogger(FqlReconService.class);

    private static final int REPAY_DAY_LIMIT = 28;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;
    @Autowired
    private FqlRepayApplyRecordRepository fqlRepayApplyRecordRepository;


    /**
     * 获取分期乐放款明细对账文件
     * @return loanVoList
     */
    public List<FqlLoanVo> getFqlLoanDetailReconFile( LocalDate localDate, FlowChannel flowChannel, BankChannel bankChannel) {
        List<FqlLoanVo> fqlLoanVoList = new ArrayList<>();
        // 首先查询当天的放款状态为SUCCEED的记录
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        List<Loan> loanList = loanRepository.findByLoanTimeBetweenAndLoanStateAndFlowChannelAndBankChannel(yesterdayStart, yesterdayEnd,
                                                                                             ProcessState.SUCCEED, flowChannel, bankChannel);
        if (loanList != null && loanList.size() > 0) {
            // 遍历这个列表
            for (Loan loan : loanList) {
                FqlLoanVo fqlLoanVo = new FqlLoanVo();
                fqlLoanVo.setOuterLoanId(loan.getOuterLoanId());
                fqlLoanVo.setLoanId(loan.getId());
                fqlLoanVo.setLoanTime(loan.getLoanTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                fqlLoanVo.setAmount(loan.getAmount());
                fqlLoanVo.setPeriods(loan.getPeriods());
                fqlLoanVo.setLoanState(loan.getLoanState());
                fqlLoanVoList.add(fqlLoanVo);
            }
        }
        logger.info("分期乐-分期乐放款明细对账文件 : {}", JsonUtil.toJsonString(fqlLoanVoList));
        return fqlLoanVoList;
    }

    // 获取分期乐还款明细对账文件
    public List<FqlRepayDetailVo> getFqlRepayDetailReconFile(LocalDate localDate, FlowChannel flowChannel, BankChannel banChannel,IsIncludingEquity includingEquity) {
        List<FqlRepayDetailVo> fqlRepayDetailVoList = new ArrayList<>();
        LocalDateTime yesterdayStart = localDate.atStartOfDay(); // 当天 00:00:00
        LocalDateTime yesterdayEnd = localDate.atTime(LocalTime.MAX); // 当天 23:59:59
        List<CustomRepayRecord> repayRecords = customRepayRecordRepository.findByRepaidDateBetweenAndRepayState(yesterdayStart, yesterdayEnd,  ProcessState.SUCCEED);
        if (repayRecords != null && repayRecords.size() > 0) {
            for (CustomRepayRecord repayRecord : repayRecords) {
                FqlRepayDetailVo fqlRepayDetailVo = new FqlRepayDetailVo();
                Loan loan = loanRepository.findById(repayRecord.getLoanId()).orElseThrow();
                if ( loan == null ) {
                    continue;
                }
                if (loan.getFlowChannel().name().equals(flowChannel.name())) {
                    fqlRepayDetailVo.setOuterRepayNo(loan.getOuterLoanId());
                    fqlRepayDetailVo.setLoanId(loan.getId());
                    fqlRepayDetailVo.setRepaidDate(repayRecord.getRepaidDate().format(DateTimeFormatter.ofPattern("yyy-MM-dd")));
                    fqlRepayDetailVo.setPeriod(repayRecord.getPeriod());
                    FqlRepayApplyRecord fqlRepayApplyRecord = fqlRepayApplyRecordRepository.findByWithholdSerialNo(repayRecord.getOuterRepayNo()).orElseThrow();
                    fqlRepayDetailVo.setRepayType(fqlRepayApplyRecord.getRepayType());
                    fqlRepayDetailVo.setTotalAmt(repayRecord.getTotalAmt());
                    fqlRepayDetailVo.setPrincipalAmt(repayRecord.getPrincipalAmt());
                    fqlRepayDetailVo.setInterestAmt(repayRecord.getInterestAmt());
                    fqlRepayDetailVo.setPenaltyAmt(repayRecord.getPenaltyAmt());
                    //add
                    fqlRepayDetailVoList.add(fqlRepayDetailVo);
                }
            }
        }
        logger.info("分期乐-分期乐还款明细对账文件 : {}", JsonUtil.toJsonString(fqlRepayDetailVoList));
        return fqlRepayDetailVoList;

    }

}
