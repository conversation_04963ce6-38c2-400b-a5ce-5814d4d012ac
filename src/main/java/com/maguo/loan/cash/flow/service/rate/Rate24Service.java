package com.maguo.loan.cash.flow.service.rate;


import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class Rate24Service implements IRateService {
    @Override
    public RateLevel rateLevel() {
        return RateLevel.RATE_24;
    }

    @Override
    public BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt, long num) {
        return BigDecimal.ZERO;
    }


    @Override
    public void trialClearSetting(Loan loan, TrialResultVo trialResultVo, List<RepayPlan> repayPlans) {
        trialResultVo.setGuaranteeFee(trialResultVo.getCapitalGuaranteeFee());
        trialResultVo.setExtraGuaranteeFee(BigDecimal.ZERO);
        trialResultVo.setConsultFee(BigDecimal.ZERO);
    }

}
