package com.maguo.loan.cash.flow.service.rate;

import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.vo.TrialResultVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IRateService {

    RateLevel rateLevel();

    /**
     * 计算咨询费
     */
    BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt,long num);


    /**
     * 结清试算设置
     */
    void trialClearSetting(Loan loan, TrialResultVo trialResultVo, List<RepayPlan> repayPlans);

}
