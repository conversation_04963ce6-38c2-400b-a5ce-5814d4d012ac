package com.maguo.loan.cash.flow.service;

import org.redisson.api.RLock;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

public class Locker {

    private final RLock lock;

    protected Locker(RLock lock) {
        this.lock = lock;
    }

    public void lock() {
        lock.lock();
    }

    public void lock(Duration leaseTime) {
        lock.lock(leaseTime.getSeconds(), TimeUnit.SECONDS);
    }

    public boolean tryLock() {
        return lock.tryLock();
    }

    public boolean tryLock(Duration waitTime) throws InterruptedException {
        return lock.tryLock(waitTime.getSeconds(), TimeUnit.SECONDS);
    }

    public boolean tryLock(Duration waitTime, Duration leaseTime) throws InterruptedException {
        return lock.tryLock(waitTime.getSeconds(), leaseTime.getSeconds(), TimeUnit.SECONDS);
    }

    public boolean isLocked() {
        return lock.isLocked();
    }

    public void unlock() {
        if (lock.isLocked()) {
            try {
                lock.unlock();
            } catch (Exception e) {
                // ignore
            }
        }
    }
}
