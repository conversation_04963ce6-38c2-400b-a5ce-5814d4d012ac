package com.maguo.loan.cash.flow.service.event;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/14
 */
public class LoanResultEvent {
    private String loanId;
    private ProcessState loanState;

    /**
     * 放款成功时间
     */
    private LocalDateTime loanTime;

    private BankChannel bankChannel;

    private String failReason;

    public LoanResultEvent(String loanId, ProcessState loanState, LocalDateTime loanTime, BankChannel bankChannel, String failReason) {
        this.loanId = loanId;
        this.loanTime = loanTime;
        this.loanState = loanState;
        this.bankChannel = bankChannel;
        this.failReason = failReason;
    }

    public String getLoanId() {
        return loanId;
    }

    public ProcessState getLoanState() {
        return loanState;
    }

    public String getFailReason() {
        return failReason;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }
}
