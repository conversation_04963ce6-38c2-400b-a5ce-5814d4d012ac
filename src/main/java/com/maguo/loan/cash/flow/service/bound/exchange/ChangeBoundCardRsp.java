package com.maguo.loan.cash.flow.service.bound.exchange;


import com.jinghang.capital.api.dto.SignStatus;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;

/**
 * <AUTHOR>
 * @date 2023/10/12
 * 换绑返回
 */
public class ChangeBoundCardRsp {
    /**
     * 确认流水号
     */
    private String confirmFlowNo;

    /**
     * 绑卡状态
     */
    private ProcessState bindState;

    /**
     * 签约状态
     */
    private SignStatus signStatus;

    /**
     * 需要再次绑卡确认
     */
    private WhetherState confirmAgain;

    /**
     * 失败原因
     */
    private String failReason;

    public String getConfirmFlowNo() {
        return confirmFlowNo;
    }

    public void setConfirmFlowNo(String confirmFlowNo) {
        this.confirmFlowNo = confirmFlowNo;
    }

    public ProcessState getBindState() {
        return bindState;
    }

    public void setBindState(ProcessState bindState) {
        this.bindState = bindState;
    }

    public WhetherState getConfirmAgain() {
        return confirmAgain;
    }

    public void setConfirmAgain(WhetherState confirmAgain) {
        this.confirmAgain = confirmAgain;
    }

    public SignStatus getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(SignStatus signStatus) {
        this.signStatus = signStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }
}
