# 项目信息管理功能实现说明

## 📋 功能概述

基于Flow系统现有的项目信息管理功能，在Cash-Manage系统中实现了完整的项目信息查询服务，为Flow和Capital系统提供统一的项目配置接口。

## 🏗️ 架构设计

### 分层架构
```
Controller层 (ProjectInfoController)
    ↓
Service层 (ProjectInfoService)
    ↓
Mapper层 (ProjectInfoMapper, ProjectElementsMapper, ProjectElementsExtMapper)
    ↓
数据库层 (project_info, project_elements, project_elements_ext)
```

### 职责分工
- **API包**: 定义接口和DTO，供外部系统调用
- **System包**: 实现具体业务逻辑和数据访问

## 📦 实现的文件结构

```
cash-manage-system/
├── src/main/java/com/jinghang/cash/
│   ├── modules/manage/controller/
│   │   └── ProjectInfoController.java          # 控制器层
│   ├── service/
│   │   └── ProjectInfoService.java            # 服务层
│   ├── mapper/
│   │   ├── ProjectInfoMapper.java             # 项目信息Mapper
│   │   ├── ProjectElementsMapper.java         # 项目要素Mapper
│   │   └── ProjectElementsExtMapper.java      # 项目要素扩展Mapper
│   └── pojo/
│       ├── ProjectInfo.java                   # 项目信息实体
│       ├── ProjectElements.java               # 项目要素实体
│       └── ProjectElementsExt.java            # 项目要素扩展实体
├── src/main/resources/mapper/
│   ├── ProjectInfoMapper.xml                  # 项目信息SQL映射
│   ├── ProjectElementsMapper.xml              # 项目要素SQL映射
│   └── ProjectElementsExtMapper.xml           # 项目要素扩展SQL映射
└── src/test/java/com/jinghang/cash/service/
    └── ProjectInfoServiceTest.java            # 服务层测试
```

## 🔧 核心功能

### 1. 项目信息查询
- **接口**: `GET /api/project/info/{projectCode}`
- **功能**: 根据项目编码查询完整的项目信息
- **返回**: 包含项目基本信息、要素信息、扩展信息的完整DTO

### 2. 缓存机制
- **Redis缓存**: 提高查询性能，减少数据库访问
- **智能过期**: 临时配置根据结束时间设置缓存过期，长期配置使用默认7天
- **缓存管理**: 提供缓存清除功能

### 3. 配置优先级
- **临时配置优先**: 优先查询有效的临时项目要素配置
- **长期配置兜底**: 无临时配置时使用长期配置
- **时效性检查**: 自动检查临时配置的有效期

## 📊 数据库表结构

### project_info (项目信息表)
```sql
CREATE TABLE project_info (
    id VARCHAR(32) PRIMARY KEY,
    project_code VARCHAR(50) NOT NULL UNIQUE,
    project_name VARCHAR(100),
    flow_channel VARCHAR(50),
    guarantee_code VARCHAR(50),
    capital_channel VARCHAR(50),
    project_type_code VARCHAR(50),
    enabled VARCHAR(20) DEFAULT 'ENABLE',
    start_date DATE,
    end_date DATE,
    remark VARCHAR(500),
    revision INT DEFAULT 0,
    created_by VARCHAR(50),
    created_time DATETIME,
    updated_by VARCHAR(50),
    updated_time DATETIME
);
```

### project_elements (项目要素表)
```sql
CREATE TABLE project_elements (
    id VARCHAR(32) PRIMARY KEY,
    project_code VARCHAR(50) NOT NULL,
    drawable_amount_range VARCHAR(50),
    drawable_amount_step VARCHAR(20),
    credit_dark_hours VARCHAR(50),
    loan_dark_hours VARCHAR(50),
    repay_dark_hours VARCHAR(50),
    funding_credit_dark_hours VARCHAR(50),
    funding_loan_dark_hours VARCHAR(50),
    funding_repay_dark_hours VARCHAR(50),
    daily_credit_limit DECIMAL(10,2),
    daily_loan_limit DECIMAL(10,2),
    credit_lock_days INT,
    loan_lock_days INT,
    customer_interest_rate VARCHAR(10),
    funding_interest_rate VARCHAR(10),
    age_range VARCHAR(20),
    supported_repay_types VARCHAR(200),
    loan_terms VARCHAR(200),
    capital_route VARCHAR(100),
    project_duration_type VARCHAR(20),
    temp_start_time DATETIME,
    temp_end_time DATETIME,
    enabled VARCHAR(20) DEFAULT 'ENABLE',
    grace_next VARCHAR(10),
    revision INT DEFAULT 0,
    created_by VARCHAR(50),
    created_time DATETIME,
    updated_by VARCHAR(50),
    updated_time DATETIME
);
```

### project_elements_ext (项目要素扩展表)
```sql
CREATE TABLE project_elements_ext (
    id VARCHAR(32) PRIMARY KEY,
    project_code VARCHAR(50) NOT NULL,
    interest_days_basis VARCHAR(10),
    allow_cross_day_repay VARCHAR(10),
    risk_model_channel VARCHAR(50),
    loan_payment_channel VARCHAR(50),
    deduction_bind_card_channel VARCHAR(50),
    deduction_merchant_code VARCHAR(50),
    sign_channel VARCHAR(50),
    overdue_sms_sender VARCHAR(50),
    sms_channel VARCHAR(50),
    grace_period_type VARCHAR(10),
    grace_period_days VARCHAR(10),
    holiday_postpone VARCHAR(10),
    credit_query_party VARCHAR(50),
    credit_report_sender VARCHAR(50),
    collection_party VARCHAR(50),
    push_collection_data VARCHAR(10),
    allow_collection_waiver VARCHAR(10),
    revision INT DEFAULT 0,
    created_by VARCHAR(50),
    created_time DATETIME,
    updated_by VARCHAR(50),
    updated_time DATETIME
);
```

## 🔍 业务逻辑

### 查询流程
1. **参数校验**: 检查项目编码是否为空
2. **缓存查询**: 先从Redis缓存中查询
3. **数据库查询**: 缓存未命中时查询数据库
   - 查询项目基本信息（只查询启用状态）
   - 查询项目要素（临时配置优先）
   - 查询项目要素扩展
4. **数据转换**: 将实体转换为DTO
5. **缓存更新**: 将查询结果放入缓存
6. **返回结果**: 返回完整的项目信息

### 缓存策略
- **缓存键**: `PROJECT_INFO_{projectCode}`
- **过期时间**: 
  - 临时配置: 到临时配置结束时间
  - 长期配置: 7天
- **缓存清除**: 提供手动清除接口

## 🚀 使用示例

### 1. 查询项目信息
```bash
GET /api/project/info/TEST001

Response:
{
  "code": "000000",
  "msg": "success",
  "data": {
    "projectCode": "TEST001",
    "projectName": "测试项目",
    "flowChannel": "FQL",
    "capitalChannel": "CYBK",
    "enabled": "ENABLE",
    "elements": {
      "drawableAmountRange": "1000-50000",
      "customerInterestRate": "24.0",
      "projectDurationType": "LONGTIME"
    },
    "elementsExt": {
      "riskModelChannel": "RISK001",
      "loanPaymentChannel": "PAY001"
    }
  }
}
```

### 2. Flow/Capital系统调用
```java
@FeignClient(name = "cash-manage", path = "/api")
public interface ProjectInfoClient extends ProjectInfoApi {
}

@Service
public class FlowProjectService {
    @Autowired
    private ProjectInfoClient projectInfoClient;
    
    public ProjectInfoDto getProjectConfig(String projectCode) {
        RestResult<ProjectInfoDto> result = projectInfoClient.queryProjectInfo(projectCode);
        return result.isSuccess() ? result.getData() : null;
    }
}
```

## 🔧 配置要求

### 1. 数据库配置
- 确保数据库中存在相应的表结构
- 配置MyBatis-Plus的Mapper扫描路径

### 2. Redis配置
- 配置Redis连接信息
- 确保RedisTemplate正确配置

### 3. 依赖配置
- 确保cash-manage-api模块正确引入
- 配置Feign客户端（如果需要）

## 🧪 测试验证

### 1. 单元测试
```bash
mvn test -Dtest=ProjectInfoServiceTest
```

### 2. 集成测试
```bash
# 启动应用后测试接口
curl -X GET http://localhost:8080/api/project/info/TEST001
```

### 3. 缓存测试
- 第一次查询：从数据库查询并缓存
- 第二次查询：从缓存获取
- 缓存清除：验证缓存清除功能

## 📝 注意事项

1. **数据一致性**: 确保数据库中的数据与Flow系统保持一致
2. **缓存管理**: 数据更新时需要清除相应的缓存
3. **异常处理**: 完善的异常处理和日志记录
4. **性能优化**: 合理设置缓存过期时间，避免缓存雪崩
5. **安全考虑**: 添加必要的权限控制和参数校验

## 🔄 与Flow系统的对应关系

| Cash-Manage | Flow系统 | 说明 |
|-------------|----------|------|
| ProjectInfoController | - | 新增的API控制器 |
| ProjectInfoService | ProjectInfoService | 业务逻辑基本一致 |
| ProjectInfoMapper | ProjectInfoRepository | 数据访问方式不同 |
| ProjectInfo | ProjectInfo | 实体结构基本一致 |
| ProjectElements | ProjectElements | 实体结构基本一致 |
| ProjectElementsExt | ProjectElementsExt | 实体结构基本一致 |

---

*实现完成时间: 2025-01-19*  
*开发团队: 金航科技开发团队*
